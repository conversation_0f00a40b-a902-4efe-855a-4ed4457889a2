2025-07-31 15:08:18,963 - __main__ - ERROR - Demo error: module 'autogen.agentchat' has no attribute 'TRACE_LOGGER_NAME'
Traceback (most recent call last):
  File "/Users/<USER>/CodeField/实习生 AI 大赛/AutoGen/autogen_diagnostic_system/run_mcp_demo.py", line 164, in <module>
    asyncio.run(main())
    ~~~~~~~~~~~^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/runners.py", line 194, in run
    return runner.run(main)
           ~~~~~~~~~~^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/base_events.py", line 721, in run_until_complete
    return future.result()
           ~~~~~~~~~~~~~^^
  File "/Users/<USER>/CodeField/实习生 AI 大赛/AutoGen/autogen_diagnostic_system/run_mcp_demo.py", line 159, in main
    await run_mcp_demo()
  File "/Users/<USER>/CodeField/实习生 AI 大赛/AutoGen/autogen_diagnostic_system/run_mcp_demo.py", line 22, in run_mcp_demo
    setup_logging()
    ~~~~~~~~~~~~~^^
  File "/Users/<USER>/CodeField/实习生 AI 大赛/AutoGen/autogen_diagnostic_system/main.py", line 38, in setup_logging
    trace_logger = logging.getLogger(autogen.agentchat.TRACE_LOGGER_NAME)
                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: module 'autogen.agentchat' has no attribute 'TRACE_LOGGER_NAME'
2025-07-31 15:09:29,615 - __main__ - INFO - Starting AutoGen OOM Diagnostic System with MCP
2025-07-31 15:09:29,615 - main - INFO - Starting OOM diagnostic for: 
            The 'checkout-service' deployment in the 'production' namespace is experiencing frequent OOMKilled events.
            Pods are restarting every few minutes and the service is becoming unavailable.
            Memory usage appears to be hitting the 2Gi limit consistently.
            Customer complaints are increasing due to failed checkout processes.
            
2025-07-31 15:09:29,670 - main - ERROR - Error during diagnostic: 1 validation error for _LLMConfig
max_retries
  Extra inputs are not permitted [type=extra_forbidden, input_value=3, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
2025-07-31 15:09:29,670 - __main__ - ERROR - Scenario 1 failed: 1 validation error for _LLMConfig
max_retries
  Extra inputs are not permitted [type=extra_forbidden, input_value=3, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
2025-07-31 15:09:34,673 - main - INFO - Starting OOM diagnostic for: 
            The 'api-gateway' service in the 'development' namespace shows gradual memory increase over time.
            Memory usage starts at 500Mi but grows to 1.5Gi over 6 hours.
            No immediate OOM events but concerning trend observed in monitoring.
            
2025-07-31 15:09:34,745 - main - ERROR - Error during diagnostic: 1 validation error for _LLMConfig
max_retries
  Extra inputs are not permitted [type=extra_forbidden, input_value=3, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
2025-07-31 15:09:34,745 - __main__ - ERROR - Scenario 2 failed: 1 validation error for _LLMConfig
max_retries
  Extra inputs are not permitted [type=extra_forbidden, input_value=3, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
2025-07-31 15:10:00,491 - __main__ - INFO - Starting AutoGen OOM Diagnostic System with MCP
2025-07-31 15:10:00,491 - main - INFO - Starting OOM diagnostic for: 
            The 'checkout-service' deployment in the 'production' namespace is experiencing frequent OOMKilled events.
            Pods are restarting every few minutes and the service is becoming unavailable.
            Memory usage appears to be hitting the 2Gi limit consistently.
            Customer complaints are increasing due to failed checkout processes.
            
2025-07-31 15:10:00,692 - main - ERROR - Error during diagnostic: 'func_or_tool' must be a function or a Tool object, got '<class 'method'>' instead.
2025-07-31 15:10:00,692 - __main__ - ERROR - Scenario 1 failed: 'func_or_tool' must be a function or a Tool object, got '<class 'method'>' instead.
2025-07-31 15:10:05,694 - main - INFO - Starting OOM diagnostic for: 
            The 'api-gateway' service in the 'development' namespace shows gradual memory increase over time.
            Memory usage starts at 500Mi but grows to 1.5Gi over 6 hours.
            No immediate OOM events but concerning trend observed in monitoring.
            
2025-07-31 15:10:05,789 - main - ERROR - Error during diagnostic: 'func_or_tool' must be a function or a Tool object, got '<class 'method'>' instead.
2025-07-31 15:10:05,789 - __main__ - ERROR - Scenario 2 failed: 'func_or_tool' must be a function or a Tool object, got '<class 'method'>' instead.
2025-07-31 15:12:54,515 - __main__ - INFO - Starting AutoGen OOM Diagnostic System with MCP
2025-07-31 15:12:54,515 - main - INFO - Starting OOM diagnostic for: 
            The 'checkout-service' deployment in the 'production' namespace is experiencing frequent OOMKilled events.
            Pods are restarting every few minutes and the service is becoming unavailable.
            Memory usage appears to be hitting the 2Gi limit consistently.
            Customer complaints are increasing due to failed checkout processes.
            
2025-07-31 15:12:54,730 - main - ERROR - Error during diagnostic: allowed_speaker_transitions_dict has values that are not lists of Agents.
2025-07-31 15:12:54,730 - __main__ - ERROR - Scenario 1 failed: allowed_speaker_transitions_dict has values that are not lists of Agents.
2025-07-31 15:12:59,730 - main - INFO - Starting OOM diagnostic for: 
            The 'api-gateway' service in the 'development' namespace shows gradual memory increase over time.
            Memory usage starts at 500Mi but grows to 1.5Gi over 6 hours.
            No immediate OOM events but concerning trend observed in monitoring.
            
2025-07-31 15:12:59,731 - main - ERROR - Error during diagnostic: allowed_speaker_transitions_dict has values that are not lists of Agents.
2025-07-31 15:12:59,731 - __main__ - ERROR - Scenario 2 failed: allowed_speaker_transitions_dict has values that are not lists of Agents.
2025-07-31 15:13:53,747 - __main__ - INFO - Testing MCP agent creation...
2025-07-31 15:13:53,747 - __main__ - INFO - Creating K8s diagnostician...
2025-07-31 15:13:53,774 - __main__ - INFO - ✓ K8s diagnostician created: K8s_Diagnostician
2025-07-31 15:13:53,774 - __main__ - INFO - Creating SLS diagnostician...
2025-07-31 15:13:53,785 - __main__ - INFO - ✓ SLS diagnostician created: SLS_Diagnostician
2025-07-31 15:13:53,785 - __main__ - INFO - Creating ARMS diagnostician...
2025-07-31 15:13:53,796 - __main__ - INFO - ✓ ARMS diagnostician created: ARMS_Diagnostician
2025-07-31 15:13:53,796 - __main__ - INFO - K8s agent details:
2025-07-31 15:13:53,796 - __main__ - INFO -   - Name: K8s_Diagnostician
2025-07-31 15:13:53,796 - __main__ - INFO -   - Type: <class 'autogen_agentchat.agents._assistant_agent.AssistantAgent'>
2025-07-31 15:13:53,796 - __main__ - INFO -   - Has model_client: False
2025-07-31 15:13:53,796 - __main__ - INFO -   - Has workbench: False
2025-07-31 15:13:53,796 - __main__ - INFO - SLS agent details:
2025-07-31 15:13:53,796 - __main__ - INFO -   - Name: SLS_Diagnostician
2025-07-31 15:13:53,796 - __main__ - INFO -   - Type: <class 'autogen_agentchat.agents._assistant_agent.AssistantAgent'>
2025-07-31 15:13:53,796 - __main__ - INFO -   - Has model_client: False
2025-07-31 15:13:53,796 - __main__ - INFO -   - Has workbench: False
2025-07-31 15:13:53,796 - __main__ - INFO - ARMS agent details:
2025-07-31 15:13:53,796 - __main__ - INFO -   - Name: ARMS_Diagnostician
2025-07-31 15:13:53,796 - __main__ - INFO -   - Type: <class 'autogen_agentchat.agents._assistant_agent.AssistantAgent'>
2025-07-31 15:13:53,796 - __main__ - INFO -   - Has model_client: False
2025-07-31 15:13:53,796 - __main__ - INFO -   - Has workbench: False
2025-07-31 15:13:53,796 - __main__ - INFO - ✅ All MCP agents created and tested successfully!
2025-07-31 15:14:18,703 - __main__ - INFO - Testing workflow initialization...
2025-07-31 15:14:18,703 - __main__ - INFO - ✓ System created
2025-07-31 15:14:18,922 - __main__ - INFO - ✓ Workflow initialized
2025-07-31 15:14:18,922 - __main__ - INFO - ✓ Agents created: ['planner', 'k8s_diagnostician', 'sls_diagnostician', 'arms_diagnostician', 'executor', 'validator']
2025-07-31 15:14:18,922 - __main__ - INFO - ✓ Diagnostic agents: ['K8s_Diagnostician', 'SLS_Diagnostician', 'ARMS_Diagnostician']
2025-07-31 15:14:18,922 - __main__ - ERROR - ❌ Error during workflow testing: allowed_speaker_transitions_dict has values that are not lists of Agents.
