"""
Basic tests for the AutoGen OOM Diagnostic System.
"""

import pytest
import sys
import os
from pathlib import Path

# Add project root and src to path for imports
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))

# Set PYTHONPATH environment variable
os.environ['PYTHONPATH'] = str(project_root / "src")

try:
    from src.core.models import OOMDiagnosticInput, DiagnosticReport, FinalReport
    from src.core.workflow import OOMDiagnosticWorkflow
    from src.agents.planner import get_planner_agent, extract_diagnostic_input
    from src.agents.diagnosticians import get_k8s_diagnostician, get_sls_diagnostician, get_arms_diagnostician
    from src.agents.executor import get_executor_agent
    from src.agents.validator import get_validator_agent
    IMPORTS_AVAILABLE = True
except ImportError as e:
    print(f"Import error: {e}")
    IMPORTS_AVAILABLE = False


@pytest.mark.skipif(not IMPORTS_AVAILABLE, reason="Required modules not available")
class TestDataModels:
    """Test Pydantic data models."""

    def test_oom_diagnostic_input_creation(self):
        """Test OOMDiagnosticInput model creation."""
        input_data = OOMDiagnosticInput(
            namespace="test-namespace",
            sls_project="test-sls",
            sls_logStore="test-logstore",
            arms_project="test-arms",
            arms_metricStore="test-metricstore",
            regionId="cn-hangzhou",
            clusterID="test-cluster",
            nodePoolID="test-nodepool",
            problem_description="Test OOM problem"
        )

        assert input_data.namespace == "test-namespace"
        assert input_data.sls_project == "test-sls"
        assert input_data.problem_description == "Test OOM problem"

    def test_diagnostic_report_creation(self):
        """Test DiagnosticReport model creation."""
        report = DiagnosticReport(
            domain="k8s",
            findings=["Pod OOMKilled", "Memory limit exceeded"],
            raw_data={"pods": [], "events": []},
            status="success"
        )

        assert report.domain == "k8s"
        assert len(report.findings) == 2
        assert report.status == "success"
        assert report.timestamp is not None

    def test_final_report_creation(self):
        """Test FinalReport model creation."""
        report = FinalReport(
            is_resolved=True,
            confidence_score=0.9,
            summary="OOM issue resolved by increasing memory limits",
            root_cause="Insufficient memory allocation",
            operations_performed=["Increased memory limit to 4Gi"],
            current_status="All pods running normally",
            long_term_recommendations=["Monitor memory usage trends"],
            reasoning="Memory usage stabilized after limit increase"
        )

        assert report.is_resolved is True
        assert report.confidence_score == 0.9
        assert "memory limits" in report.summary


class TestAgentCreation:
    """Test agent creation and initialization."""

    def test_planner_agent_creation(self):
        """Test planner agent creation."""
        planner = get_planner_agent()
        assert planner.name == "Planner"
        assert "master" in planner.system_message.lower()

    def test_diagnostic_agents_creation(self):
        """Test diagnostic agents creation."""
        k8s_agent = get_k8s_diagnostician()
        sls_agent = get_sls_diagnostician()
        arms_agent = get_arms_diagnostician()

        assert k8s_agent.name == "K8s_Diagnostician"
        assert sls_agent.name == "SLS_Diagnostician"
        assert arms_agent.name == "ARMS_Diagnostician"

        # Check that diagnostic tools are registered
        assert hasattr(k8s_agent, '_function_map')
        assert hasattr(sls_agent, '_function_map')
        assert hasattr(arms_agent, '_function_map')

    def test_executor_agent_creation(self):
        """Test executor agent creation."""
        executor = get_executor_agent()
        assert executor.name == "Executor"
        assert executor.human_input_mode == "NEVER"

    def test_validator_agent_creation(self):
        """Test validator agent creation."""
        validator = get_validator_agent()
        assert validator.name == "Validator"
        assert "sre" in validator.system_message.lower()


class TestWorkflow:
    """Test workflow management."""

    def test_workflow_initialization(self):
        """Test workflow initialization."""
        workflow = OOMDiagnosticWorkflow()

        assert workflow.state.current_phase == "planning"
        assert len(workflow.agents) == 6
        assert len(workflow.diagnostic_agents) == 3
        assert len(workflow.state.completed_diagnostics) == 0

    def test_workflow_status(self):
        """Test workflow status reporting."""
        workflow = OOMDiagnosticWorkflow()
        status = workflow.get_workflow_status()

        assert status["current_phase"] == "planning"
        assert status["total_diagnostics"] == 3
        assert status["diagnostic_completion"] == 0.0
        assert status["workflow_complete"] is False

    def test_workflow_reset(self):
        """Test workflow reset functionality."""
        workflow = OOMDiagnosticWorkflow()

        # Simulate some progress
        workflow.state.current_phase = "executing"
        workflow.state.completed_diagnostics = ["k8s", "sls"]

        # Reset workflow
        workflow.reset_workflow()

        assert workflow.state.current_phase == "planning"
        assert len(workflow.state.completed_diagnostics) == 0

    def test_group_chat_creation(self):
        """Test group chat creation."""
        workflow = OOMDiagnosticWorkflow()
        groupchat = workflow.create_group_chat()

        assert len(groupchat.agents) == 6
        assert groupchat.max_round == 20
        assert groupchat.speaker_selection_method == workflow.custom_speaker_selection

    def test_termination_message_detection(self):
        """Test termination message detection."""
        workflow = OOMDiagnosticWorkflow()

        # Test positive cases
        assert workflow.is_termination_message(
            {"content": "FINAL REPORT generated"})
        assert workflow.is_termination_message(
            {"content": "WORKFLOW COMPLETE"})
        assert workflow.is_termination_message({"content": "TERMINATE"})

        # Test negative cases
        assert not workflow.is_termination_message(
            {"content": "Still working on diagnosis"})
        assert not workflow.is_termination_message(
            {"content": "Partial results available"})


class TestUtilityFunctions:
    """Test utility functions."""

    def test_extract_diagnostic_input(self):
        """Test diagnostic input extraction."""
        problem = "OOM issues in production namespace"
        input_data = extract_diagnostic_input(problem)

        assert isinstance(input_data, OOMDiagnosticInput)
        assert input_data.problem_description == problem
        assert input_data.namespace == "default"  # Default value


if __name__ == "__main__":
    pytest.main([__file__])
