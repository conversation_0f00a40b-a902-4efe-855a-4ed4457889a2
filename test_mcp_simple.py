#!/usr/bin/env python3
"""
Simple test for MCP integration without full workflow.
"""

import asyncio
import logging
from main import setup_logging
from src.agents.diagnosticians import (
    get_k8s_diagnostician_with_workbench,
    get_sls_diagnostician_with_workbench,
    get_arms_diagnostician_with_workbench
)

async def test_mcp_agents():
    """Test MCP agent creation and basic functionality."""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    try:
        logger.info("Testing MCP agent creation...")
        
        # Test K8s agent
        logger.info("Creating K8s diagnostician...")
        k8s_agent, k8s_workbench = await get_k8s_diagnostician_with_workbench()
        logger.info(f"✓ K8s diagnostician created: {k8s_agent.name}")
        
        # Test SLS agent
        logger.info("Creating SLS diagnostician...")
        sls_agent, sls_workbench = await get_sls_diagnostician_with_workbench()
        logger.info(f"✓ SLS diagnostician created: {sls_agent.name}")
        
        # Test ARMS agent
        logger.info("Creating ARMS diagnostician...")
        arms_agent, arms_workbench = await get_arms_diagnostician_with_workbench()
        logger.info(f"✓ ARMS diagnostician created: {arms_agent.name}")
        
        # Test basic agent properties
        agents = [
            ("K8s", k8s_agent),
            ("SLS", sls_agent),
            ("ARMS", arms_agent)
        ]
        
        for name, agent in agents:
            logger.info(f"{name} agent details:")
            logger.info(f"  - Name: {agent.name}")
            logger.info(f"  - Type: {type(agent)}")
            logger.info(f"  - Has model_client: {hasattr(agent, 'model_client')}")
            logger.info(f"  - Has workbench: {hasattr(agent, 'workbench')}")
        
        # Cleanup workbenches
        workbenches = [k8s_workbench, sls_workbench, arms_workbench]
        for i, workbench in enumerate(workbenches):
            if hasattr(workbench, 'close'):
                logger.info(f"Closing workbench {i+1}...")
                await workbench.close()
        
        logger.info("✅ All MCP agents created and tested successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error during MCP agent testing: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_mcp_agents())
    if success:
        print("\n🎉 MCP integration test completed successfully!")
    else:
        print("\n💥 MCP integration test failed!")
        exit(1)
