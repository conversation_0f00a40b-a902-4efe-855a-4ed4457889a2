#!/usr/bin/env python3
"""
Simple test for workflow initialization without full conversation.
"""

import asyncio
import logging
from main import setup_logging, OOMDiagnosticSystemMCP

async def test_workflow_initialization():
    """Test workflow initialization and basic setup."""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    try:
        logger.info("Testing workflow initialization...")
        
        # Create system
        system = OOMDiagnosticSystemMCP()
        logger.info("✓ System created")
        
        # Initialize workflow
        await system.workflow.initialize()
        logger.info("✓ Workflow initialized")
        
        # Check agents
        agents = system.workflow.agents
        logger.info(f"✓ Agents created: {list(agents.keys())}")
        
        # Check diagnostic agents
        diagnostic_agents = system.workflow.diagnostic_agents
        logger.info(f"✓ Diagnostic agents: {[agent.name for agent in diagnostic_agents]}")
        
        # Test group chat creation
        groupchat = system.workflow.create_group_chat(max_rounds=5)
        logger.info(f"✓ Group chat created with {len(groupchat.agents)} agents")
        
        # Test manager creation
        manager = system.workflow.create_manager(groupchat)
        logger.info(f"✓ Manager created: {type(manager)}")
        
        # Cleanup
        await system.workflow.cleanup()
        logger.info("✓ Cleanup completed")
        
        logger.info("✅ Workflow initialization test completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error during workflow testing: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_workflow_initialization())
    if success:
        print("\n🎉 Workflow initialization test completed successfully!")
    else:
        print("\n💥 Workflow initialization test failed!")
        exit(1)
