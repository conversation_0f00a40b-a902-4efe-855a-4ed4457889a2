# AutoGen OOM Diagnostic System

A Multi-Agent Kubernetes OOM (Out of Memory) problem closed-loop troubleshooting system built with Microsoft AutoGen framework with MCP (Model Context Protocol) integration.

## Overview

This system implements a sophisticated multi-agent workflow for diagnosing and resolving Kubernetes OOM issues following the "Diagnose → Propose → Execute → Verify" engineering loop. It uses five specialized agents working together:

1. **Planner** - Analyzes problems and coordinates the workflow
2. **K8s Diagnostician** - Kubernetes cluster and pod analysis
3. **SLS Diagnostician** - Log analysis and pattern detection
4. **ARMS Diagnostician** - Memory metrics and performance monitoring
5. **Executor** - Executes remediation plans
6. **Validator** - Verifies fixes and generates final reports

## Features

- **MCP Integration** - Uses Model Context Protocol for streamable tool communication
- **State-driven workflow** with deterministic agent transitions
- **Parallel diagnostic execution** across multiple domains
- **Structured data exchange** using Pydantic models
- **Comprehensive logging** and observability
- **Extensible architecture** for adding new diagnostic capabilities
- **Production-ready** error handling and recovery
- **Streamable HTTP Support** - Real-time tool communication via MCP endpoints

## Architecture

The system uses AutoGen's GroupChat with a custom speaker selection function to implement a finite state machine that controls the workflow:

```
PLANNING → DIAGNOSING → EXECUTING → VALIDATING → COMPLETED
```

Each phase has specific agents responsible for different aspects of the OOM troubleshooting process.

## Installation

1. **Prerequisites**:
   - Python 3.13+
   - uv package manager
   - Access to Qwen LLM service (OpenAI-compatible API)

2. **Setup**:
   ```bash
   # Clone and enter the project directory
   cd autogen_diagnostic_system

   # Install dependencies
   uv sync

   # Copy and configure environment variables
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Configuration**:
   Edit `.env` file with your settings:
   ```env
   # LLM Configuration
   QWEN_BASE_URL=http://your-qwen-service:8000/v1
   QWEN_API_KEY=your_api_key

   # MCP Server Configuration (Primary)
   MCP_SERVER_URL=http://localhost:8080/mcp
   MCP_SERVER_AUTH=Bearer your-mcp-token-here
   MCP_TIMEOUT=30.0
   MCP_SSE_READ_TIMEOUT=300.0

   # Legacy Tool Server URLs (for backward compatibility)
   K8S_TOOL_SERVER_URL=http://localhost:8081/diagnose
   SLS_TOOL_SERVER_URL=http://localhost:8082/diagnose
   ARMS_TOOL_SERVER_URL=http://localhost:8083/diagnose
   ```

## Usage

### Basic Usage

#### Using MCP Integration (Recommended)

```bash
# Run the MCP-enabled diagnostic system
uv run python main.py

# Or run the MCP demo with multiple scenarios
uv run python run_mcp_demo.py
```

#### Using Legacy Mode

```bash
# Run with mock servers for testing
uv run python run_demo.py
```

### Programmatic Usage

```python
from src.core.workflow import OOMDiagnosticWorkflow
from main import OOMDiagnosticSystem

# Create system instance
system = OOMDiagnosticSystem()

# Run diagnostic
problem = "Pods in namespace 'production' are experiencing OOMKilled events"
result = await system.run_diagnostic(problem)

if result["success"]:
    print("Diagnostic completed successfully")
    print(f"Status: {result['workflow_status']}")
else:
    print(f"Diagnostic failed: {result['error']}")
```

## Project Structure

```
autogen_diagnostic_system/
├── src/
│   ├── agents/           # Agent implementations
│   │   ├── planner.py
│   │   ├── diagnosticians.py
│   │   ├── executor.py
│   │   └── validator.py
│   ├── core/            # Core system components
│   │   ├── config.py    # Configuration management
│   │   ├── models.py    # Pydantic data models
│   │   └── workflow.py  # Workflow orchestration
│   ├── tools/           # External tool integrations
│   └── utils/           # Utility functions
├── tests/               # Test suite
├── logs/               # Log files
├── main.py             # Main entry point
├── .env.example        # Environment template
└── README.md
```

## Agent Responsibilities

### Planner Agent
- Analyzes initial OOM problem reports
- Extracts diagnostic parameters
- Creates structured diagnostic plans
- Coordinates the overall workflow

### Diagnostic Agents

**K8s Diagnostician**:
- Pod status analysis (OOMKilled, CrashLoopBackOff)
- Resource limits and requests examination
- Node capacity and memory pressure analysis
- Kubernetes events investigation

**SLS Diagnostician**:
- Log analysis for OOM-related events
- Error pattern detection
- Log aggregation and trend analysis
- Correlation with OOM occurrences

**ARMS Diagnostician**:
- Memory usage metrics and trends
- Container memory consumption patterns
- Node-level memory pressure monitoring
- Performance correlation analysis

### Executor Agent
- Executes remediation plans
- Patches deployment resource limits
- Scales node pools when needed
- Provides detailed execution feedback

### Validator Agent
- Verifies remediation effectiveness
- Generates structured final reports
- Provides confidence scoring
- Offers long-term recommendations

## External Tool Integration

The system integrates with external diagnostic tools through HTTP APIs:

- **K8s Tools**: kubectl operations, cluster analysis
- **SLS Tools**: Log querying and analysis
- **ARMS Tools**: Metrics collection and analysis

Each tool server should implement the diagnostic API contract:

```json
POST /diagnose
{
  "problem": "description",
  "namespace": "default",
  "domain": "k8s|sls|arms"
}
```

## Configuration

### Environment Variables

| Variable | Required | Description |
|----------|----------|-------------|
| `QWEN_BASE_URL` | Yes | Qwen LLM service URL |
| `QWEN_API_KEY` | No | API key for Qwen service |
| `K8S_TOOL_SERVER_URL` | Yes | K8s diagnostic tool URL |
| `SLS_TOOL_SERVER_URL` | Yes | SLS diagnostic tool URL |
| `ARMS_TOOL_SERVER_URL` | Yes | ARMS diagnostic tool URL |
| `DEFAULT_NAMESPACE` | No | Default K8s namespace |
| `CLUSTER_ID` | Yes | Cluster ID for node operations |
| `NODE_POOL_ID` | Yes | Node pool ID for scaling |

### LLM Configuration

The system uses Qwen models through OpenAI-compatible APIs. Configure your model service to provide:

- Chat completions endpoint
- Function calling support
- Structured output capabilities

## Development

### Adding New Diagnostic Agents

1. Create agent class in `src/agents/`
2. Implement diagnostic tools
3. Register with workflow in `src/core/workflow.py`
4. Update speaker selection logic

### Adding New Tools

1. Create tool class in `src/tools/`
2. Implement HTTP client for external service
3. Register tool with appropriate agent
4. Add configuration variables

### Testing

```bash
# Run tests
uv run pytest tests/

# Run with coverage
uv run pytest --cov=src tests/
```

## Quick Start

To get started quickly with mock tool servers:

```bash
# 1. Copy environment template
cp .env.example .env

# 2. Edit .env with your Qwen service URL
# QWEN_BASE_URL=http://your-qwen-service:8000/v1

# 3. Run the system (uses mock tool servers by default)
uv run python main.py
```

The system will run with simulated diagnostic tools for demonstration purposes.

## License

This project is licensed under the MIT License.