#!/usr/bin/env python3
"""
Demo script for the AutoGen OOM Diagnostic System with MCP integration.
This script demonstrates the MCP-based multi-agent diagnostic workflow.
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from main import OOMDiagnosticSystemMCP, setup_logging


async def run_mcp_demo():
    """Run a demonstration of the MCP-based OOM diagnostic system."""
    
    # Setup logging
    setup_logging()
    logger = logging.getLogger(__name__)
    
    logger.info("Starting AutoGen OOM Diagnostic System with MCP")
    
    # Test scenarios
    test_scenarios = [
        {
            "name": "Production OOM Crisis",
            "description": """
            The 'checkout-service' deployment in the 'production' namespace is experiencing frequent OOMKilled events.
            Pods are restarting every few minutes and the service is becoming unavailable.
            Memory usage appears to be hitting the 2Gi limit consistently.
            Customer complaints are increasing due to failed checkout processes.
            """
        },
        {
            "name": "Development Memory Leak",
            "description": """
            The 'api-gateway' service in the 'development' namespace shows gradual memory increase over time.
            Memory usage starts at 500Mi but grows to 1.5Gi over 6 hours.
            No immediate OOM events but concerning trend observed in monitoring.
            """
        }
    ]
    
    # Create the diagnostic system
    system = OOMDiagnosticSystemMCP()
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n{'='*80}")
        print(f"RUNNING SCENARIO {i}: {scenario['name']}")
        print(f"{'='*80}")
        
        try:
            # Run the diagnostic workflow
            result = await system.run_diagnostic(scenario['description'])
            
            if result["status"] == "completed":
                logger.info(f"Scenario {i} completed successfully")
                print(f"\n✅ Scenario {i} Status: {result['status']}")
                print(f"📊 Final Phase: {result['phase']}")
                
                # Display diagnostic reports
                if result.get('diagnostic_reports'):
                    print(f"\n📋 Diagnostic Reports:")
                    for domain, report in result['diagnostic_reports'].items():
                        print(f"  - {domain.upper()}: {report.get('status', 'Unknown')}")
                
                # Display execution plan
                if result.get('execution_plan'):
                    print(f"\n🔧 Execution Plan: {result['execution_plan'].get('summary', 'No summary')}")
                
                # Display final report
                if result.get('final_report'):
                    print(f"\n📄 Final Report: {result['final_report'].get('summary', 'No summary')}")
                
                # Display conversation summary
                if result.get('conversation_history'):
                    print(f"\n💬 Conversation Messages: {len(result['conversation_history'])}")
                
            else:
                logger.error(f"Scenario {i} failed: {result.get('error', 'Unknown error')}")
                print(f"\n❌ Scenario {i} failed: {result.get('error', 'Unknown error')}")
                
        except Exception as e:
            logger.error(f"Error in scenario {i}: {str(e)}")
            print(f"\n❌ Error in scenario {i}: {str(e)}")
        
        # Add delay between scenarios
        if i < len(test_scenarios):
            print(f"\nWaiting 5 seconds before next scenario...")
            await asyncio.sleep(5)
    
    print(f"\n{'='*80}")
    print("MCP DEMO COMPLETED")
    print(f"{'='*80}")


async def test_mcp_connection():
    """Test MCP server connection."""
    
    print("Testing MCP server connection...")
    
    try:
        from src.core.config import get_mcp_server_params
        from autogen_ext.tools.mcp import McpWorkbench
        
        # Create MCP workbench
        mcp_params = get_mcp_server_params()
        workbench = McpWorkbench(server_params=mcp_params)
        
        print(f"✅ MCP workbench created successfully")
        print(f"📡 Server URL: {mcp_params.url}")
        print(f"⏱️  Timeout: {mcp_params.timeout}s")
        print(f"📺 SSE Read Timeout: {mcp_params.sse_read_timeout}s")
        
        # Try to get available tools (if the server is running)
        try:
            # This would require the actual MCP server to be running
            # For now, just test the workbench creation
            print("🔧 MCP workbench ready for tool integration")
        except Exception as e:
            print(f"⚠️  MCP server not available (expected if not running): {e}")
        
        # Cleanup
        if hasattr(workbench, 'close'):
            await workbench.close()
            
        return True
        
    except Exception as e:
        print(f"❌ MCP connection test failed: {e}")
        return False


async def main():
    """Main function for the MCP demo."""
    
    print("AutoGen OOM Diagnostic System - MCP Integration Demo")
    print("=" * 60)
    
    # Test MCP connection first
    print("\n1. Testing MCP Connection...")
    connection_ok = await test_mcp_connection()
    
    if not connection_ok:
        print("\n⚠️  MCP connection test failed. The demo will continue but may not work properly.")
        print("   Make sure your MCP server is running and configured correctly.")
        
        response = input("\nContinue with demo anyway? (y/N): ")
        if response.lower() != 'y':
            print("Demo cancelled.")
            return
    
    # Run the demo
    print("\n2. Running MCP Demo Scenarios...")
    await run_mcp_demo()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\nDemo interrupted by user.")
    except Exception as e:
        print(f"\n\nDemo failed with error: {e}")
        logging.getLogger(__name__).error(f"Demo error: {e}", exc_info=True)
