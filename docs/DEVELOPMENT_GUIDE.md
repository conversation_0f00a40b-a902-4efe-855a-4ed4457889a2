# K8s SRE 多智能体助手开发指南

## 1. 简介

本文档将指导您从零开始，使用 LangGraph 和多智能体（Multi-Agent）架构，构建一个强大的 K8s SRE 助手。该助手能够模拟 SRE 专家，自动化处理复杂的 OOM（Out of Memory）问题排查、方案制定、执行和报告的全流程。

### 核心特性

- **Supervisor 架构**：一个主管智能体负责任务的路由和协调，确保流程正确、高效。
- **专家分工**：多个专门的智能体（诊断、操作）各司其职，保证了任务处理的专业性和模块化。
- **工具解耦**：所有外部依赖（k8s, SLS, ARMS）通过您提供的 MCP Server URL 接入，使得核心逻辑与具体实现分离。
- **安全第一**：在执行任何可能产生影响的操作前，引入“人机校验”（Human-in-the-loop）环节，确保所有变更都经过您的批准。
- **代码精简**：遵循模块化设计，将核心逻辑代码控制在 500 行以内。

### 最终架构

```mermaid
graph TD
    subgraph "K8s SRE Multi-Agent System (LangGraph)"
        direction TB
        UserInput["👨‍💻 User Input"] -- "1. Invoke" --> SupervisorGraph
        subgraph SupervisorGraph ["`StateGraph` with `SREMultiAgentState`"]
            direction LR
            Supervisor["👨‍💼 SRE Supervisor"]
            subgraph DiagnosticAgents ["Diagnostic Agents"]
                direction TB
                K8sAgent["⚙️ k8s_diagnostic_agent"]
                SLSAgent["📋 sls_diagnostic_agent"]
                ARMSAgent["📊 arms_diagnostic_agent"]
            end
            subgraph ExecutionAgent ["Execution Agent"]
                OpsAgent["🔩 k8s_operations_agent"]
            end
            HumanInLoop["🧑‍💻 Human-in-the-loop (Approval)"]
            Supervisor -- "2. Delegate Diagnosis" --> DiagnosticAgents
            DiagnosticAgents -- "3. Return Findings" --> Supervisor
            Supervisor -- "4. Propose Plan & Await Approval" --> HumanInLoop
            HumanInLoop -- "5. Grant Approval" --> Supervisor
            Supervisor -- "6. Delegate Execution" --> ExecutionAgent
            ExecutionAgent -- "7. Return Execution Result" --> Supervisor
        end
        Supervisor -- "8. Generate Final Report" --> FinalResponse["📝 Final Report"]
    end
    subgraph "External Tools (via MCP)"
        K8sMCP["MCP Server URL: k8s_tools"]
        SLSMCP["MCP Server URL: sls_tools"]
        ARMSMCP["MCP Server URL: arms_tools"]
    end
    K8sAgent -- "Calls Tool" --> K8sMCP
    SLSAgent -- "Calls Tool" --> SLSMCP
    ARMSAgent -- "Calls Tool" --> ARMSMCP
    OpsAgent -- "Calls Tool" --> K8sMCP
```

---

## 2. 项目结构

为了保持代码的清晰和可维护性，我们采用以下目录结构。

```
k8s-sre-agent/
├── src/
│   ├── __init__.py
│   ├── agents/
│   │   ├── __init__.py
│   │   ├── specialist_agents.py   # 定义所有 Specialist Agents
│   │   └── supervisor.py          # 定义 Supervisor Agent
│   ├── prompts.py                 # 集中管理所有 Prompt
│   ├── llm.py                     # LLM 配置
│   └── state.py                   # 定义 Graph 的 State
├── e_main.py                      # 主应用入口，组装和运行 Graph
├── DEVELOPMENT_GUIDE.md           # 开发指南 (本文档)
├── pyproject.toml                 # 项目依赖与元数据
├── .env.example                   # 环境变量示例
└── README.md
```

---

## 3. 环境与配置

### 3.1. Python 环境

建议使用 `uv` 来管理虚拟环境和依赖。

```bash
# 安装 uv
pip install uv
# 创建并激活虚拟环境
uv venv
source .venv/bin/activate
```

### 3.2. 项目依赖

**`pyproject.toml`**:
```toml
[project]
name = "k8s-sre-agent"
version = "0.1.0"
description = "A multi-agent SRE assistant for Kubernetes OOM troubleshooting."
authors = [{ name = "Your Name", email = "<EMAIL>" }]
requires-python = ">=3.9"
dependencies = [
    "langgraph",
    "langchain",
    "langchain-openai",
    "langchain_anthropic",
    "langgraph-supervisor",
    "langchain-mcp-adapters", # 连接 MCP Server
    "uv",
    "python-dotenv",
]
```
使用 `uv` 安装依赖：
```bash
uv pip install -r pyproject.toml
```

### 3.3. 配置 API 密钥和工具 URL

在项目根目录创建 `.env` 文件，存放您的 API 密钥和工具服务器的 URL。

**`.env.example`**:
```
# Qwen API Key from Aliyun Dashscope
QWEN_API_KEY="sk-your-key-here"

# URLs for your pre-running MCP tool servers
K8S_TOOL_URL="http://localhost:8001/mcp"
SLS_TOOL_URL="http://localhost:8002/mcp"
ARMS_TOOL_URL="http://localhost:8003/mcp"
```
**请根据您的实际情况修改 `.env` 文件中的 URL。**

---

## 4. 定义 Prompts 和 LLM

### 4.1. 配置 LLM (Qwen)

**`src/llm.py`**:
```python
import os
from langchain_openai import ChatOpenAI
from dotenv import load_dotenv
from langchain_core.language_models.chat_models import BaseChatModel

load_dotenv()

def get_llm() -> BaseChatModel:
    api_key = os.getenv("QWEN_API_KEY")
    if not api_key:
        raise ValueError("QWEN_API_KEY is not set in the environment variables.")

    llm = ChatOpenAI(
        model="qwen-plus",
        openai_api_base="https://dashscope.aliyuncs.com/compatible-mode/v1",
        openai_api_key=api_key,
        temperature=0,
        streaming=True,
    )
    return llm

LLM = get_llm()
```

### 4.2. 集中管理 Prompts

**`src/prompts.py`**:
```python
# SRE Supervisor Prompt
SUPERVISOR_PROMPT = """你是一个专家级的 K8s SRE 主管...""" # (内容同前，此处省略)

# Specialist Agent Prompts
K8S_DIAG_PROMPT = """你是一名 K8s 诊断专家...""" # (内容同前)
SLS_DIAG_PROMPT = """你是一名 SLS 日志分析专家...""" # (内容同前)
ARMS_DIAG_PROMPT = """你是一名 ARMS 监控分析专家...""" # (内容同前)
K8S_OPS_PROMPT = """你是一名 K8s 操作专家...""" # (内容同前)
```

---

## 5. 实现 Specialist Agents

### 5.1. 定义 Graph State

**`src/state.py`**:
```python
from typing import TypedDict, Annotated, List
from langgraph.graph.message import AnyMessage

class SREMultiAgentState(TypedDict):
    messages: Annotated[List[AnyMessage], lambda x, y: x + y]
    task: str
    supervisor_report: str
```

### 5.2. 创建 Specialist Agents

**`src/agents/specialist_agents.py`**:
```python
import os
from langchain_core.tools import BaseTool
from langgraph.prebuilt import create_react_agent
from langchain_mcp_adapters.client import MultiServerMCPClient
from dotenv import load_dotenv
from ..llm import LLM
from ..prompts import K8S_DIAG_PROMPT, SLS_DIAG_PROMPT, ARMS_DIAG_PROMPT, K8S_OPS_PROMPT

load_dotenv()

def get_mcp_tools():
    k8s_url = os.getenv("K8S_TOOL_URL")
    sls_url = os.getenv("SLS_TOOL_URL")
    arms_url = os.getenv("ARMS_TOOL_URL")
    if not all([k8s_url, sls_url, arms_url]):
        raise ValueError("One or more MCP Tool URLs are not set in the environment.")

    client = MultiServerMCPClient({
        "k8s_tools": {"url": k8s_url, "transport": "streamable_http"},
        "sls_tools": {"url": sls_url, "transport": "streamable_http"},
        "arms_tools": {"url": arms_url, "transport": "streamable_http"},
    })
    return client.get_tools_sync()

ALL_TOOLS: list[BaseTool] = get_mcp_tools()
TOOL_MAP = {tool.name: tool for tool in ALL_TOOLS}

k8s_diag_tools = [TOOL_MAP["kubectl_get_pods"], TOOL_MAP["kubectl_describe_pod"]]
sls_diag_tools = [TOOL_MAP["query_sls_logs"]]
arms_diag_tools = [TOOL_MAP["query_arms_metrics"]]
k8s_ops_tools = [TOOL_MAP["kubectl_patch_deployment_resources"], TOOL_MAP["cs_autoscaler_scale_nodepool"]]

k8s_diagnostic_agent = create_react_agent(LLM, k8s_diag_tools, K8S_DIAG_PROMPT)
sls_diagnostic_agent = create_react_agent(LLM, sls_diag_tools, SLS_DIAG_PROMPT)
arms_diagnostic_agent = create_react_agent(LLM, arms_diag_tools, ARMS_DIAG_PROMPT)
k8s_operations_agent = create_react_agent(LLM, k8s_ops_tools, K8S_OPS_PROMPT)
```

---

## 6. 实现 Supervisor Agent 和人机交互

**`src/agents/supervisor.py`**:
```python
from langgraph_supervisor import create_supervisor
from ..llm import LLM
from ..prompts import SUPERVISOR_PROMPT
from .specialist_agents import k8s_diagnostic_agent, sls_diagnostic_agent, arms_diagnostic_agent, k8s_operations_agent

members = ["k8s_diagnostic_agent", "sls_diagnostic_agent", "arms_diagnostic_agent", "k8s_operations_agent"]

sre_supervisor_agent = create_supervisor(
    llm=LLM,
    prompt=SUPERVISOR_PROMPT,
    agent_names=members,
    system_prompt="You are a supervisor of a team of SRE agents...",
    agents={
        "k8s_diagnostic_agent": k8s_diagnostic_agent,
        "sls_diagnostic_agent": sls_diagnostic_agent,
        "arms_diagnostic_agent": arms_diagnostic_agent,
        "k8s_operations_agent": k8s_operations_agent,
    }
)
```

---

## 7. 组装与运行

**`e_main.py`**:
```python
import uuid
import json
from typing import Literal
from langgraph.graph import StateGraph, START
from langgraph.checkpoint.memory import InMemorySaver
from langgraph.graph.message import ToolMessage
from langgraph.interrupt import interrupt
from src.state import SREMultiAgentState
from src.agents.supervisor import sre_supervisor_agent, members

def human_in_the_loop(state: SREMultiAgentState):
    print("\n--- HUMAN APPROVAL REQUIRED ---")
    print("Supervisor's proposal:\n", state['messages'][-1].content)
    interrupt()
    return

def should_continue(state: SREMultiAgentState) -> Literal["supervisor", "human_in_the_loop"]:
    if not state["messages"] or isinstance(state["messages"][-1], ToolMessage):
        return "supervisor"
    last_message = state["messages"][-1]
    if last_message.tool_calls:
        return "supervisor"
    content = last_message.content.lower()
    if "k8s_operations_agent" in content and ("plan" in content or "propose" in content):
        return "human_in_the_loop"
    return "supervisor"

builder = StateGraph(SREMultiAgentState)
builder.add_node("supervisor", sre_supervisor_agent)
builder.add_node("human_in_the_loop", human_in_the_loop)
builder.add_conditional_edges("supervisor", should_continue)
builder.add_edge("human_in_the_loop", "supervisor")
for member in members:
    builder.add_edge(member, "supervisor")
builder.add_edge(START, "supervisor")

graph = builder.compile(checkpointer=InMemorySaver())

def run_graph(input_message: str):
    thread_id = str(uuid.uuid4())
    config = {"configurable": {"thread_id": thread_id}}
    initial_state = {"messages": [("user", input_message)]}

    for event in graph.stream(initial_state, config=config, stream_mode="values"):
        print("---")
        print(f"Node: {list(event.keys())[0]}")
        print(json.dumps(event, indent=2, default=str))

        if "human_in_the_loop" in event:
            print("\nGraph paused. Type 'yes' to approve.")
            if input("> ").lower() == 'yes':
                graph.update_state(config, None)
            else:
                print("Execution aborted.")
                break

if __name__ == "__main__":
    run_graph("A pod in 'prod' is OOMKilled. Params: sls_project: k8s-logs...")
```

---

## 8. 总结与展望

恭喜！您已成功设计出一个模块化、可扩展的 K8s SRE 多智能体助手。

### 未来展望
- **真实工具对接**: 您只需确保您的 MCP 工具服务器在指定的 URL 上运行即可，Agent 代码无需改动。
- **增加专家**: 可以方便地增加新专家，只需在 `specialist_agents.py` 中创建，并在 `supervisor.py` 中注册。

希望这份修正后的指南对您有所帮助！祝您编码愉快！