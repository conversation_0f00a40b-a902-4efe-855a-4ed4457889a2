

# **使用 AutoGen 构建弹性多 Agent 诊断系统：一份全面的开发与迁移指南**

---

## **引言：从显式图到对话式编排的范式演进**

将一个复杂的编排系统从 LangGraph 迁移到 AutoGen，并非简单的框架替换，而是一次深刻的战略性架构演进。此举的核心在于理解两种框架在设计哲学上的根本差异，并利用 AutoGen 的独特优势来构建一个既灵活又稳定的系统。本指南旨在提供一个从理论到实践的完整蓝图，不仅阐述“如何做”，更深入剖G析“为何如此做”。

### **架构范式分析：LangGraph 与 AutoGen**

LangGraph 的核心是一种以状态机为中心的思想，它将工作流建模为显式的有向无环图（DAG）。在这个图中，每个节点代表一个 Agent 或一个操作，而边则定义了严格的控制流 1。这种方法的优势在于其精确性和可预测性：系统的每一个步骤和转换路径都被明确预定义，非常适合确定性的、线性的业务流程 2。然而，当面对复杂、多变的诊断任务时，这种刚性结构可能成为一种束缚。每当出现新的诊断场景或需要调整处理逻辑时，修改和扩展一个庞大而复杂的 DAG 可能会变得异常繁琐。

与此相反，AutoGen 建立在一个以对话为中心的模型之上 2。它将多 Agent 协作视为一场动态的对话，不同的 Agent 根据对话的上下文和自身角色参与其中。这种模式天然地支持更灵活、更具适应性的交互，类似于一个专家团队围绕一个问题进行会诊 4。然而，纯粹的、无约束的对话式协作也存在风险。在生产环境中，不受控制的对话可能导致无限循环、次优的 Agent 选择，或偏离核心任务，这与系统稳定性的要求背道而驰 5。

### **迁移的合理性论证**

本次迁移的根本动机在于，对于复杂的运维诊断场景，经过适当约束的 AutoGen 对话模型能够提供一种优于刚性图模型的架构。运维诊断本质上是一个探索性和迭代性的过程，而非一成不变的流水线。它需要不同领域的专家（如 k8s、sls、arms 诊断 Agent）根据初步发现动态地介入、交换信息并修正假设。

AutoGen 的架构允许我们构建一个既有结构又具弹性的系统。通过利用其先进的编排模式，如基于有限状态机（FSM）的 GroupChat 6 或自定义的发言人选择逻辑 8，我们可以在灵活的对话范式之上施加一个确定性的高阶控制流。这意味着我们可以定义一个清晰的宏观流程（规划 -> 诊断 -> 执行 -> 验证），同时在每个阶段内部，允许 Agent 之间进行丰富的、动态的、迭代式的对话。这种“宏观结构化、微观灵活性”的结合，为解决复杂问题提供了一个比纯粹的图模型或纯粹的自由对话模型更为先进和可维护的解决方案。

下表总结了两种框架在核心理念上的差异及其对本项目的具体影响。

| 特性 | LangGraph 方法 | AutoGen 方法 | 对本项目的影响 |
| :---- | :---- | :---- | :---- |
| **工作流建模** | 基于图的 DAG，节点为 Agent，边为控制流 1 | 对话驱动，Agent 间通过消息传递协作 2 | AutoGen 更自然地模拟了诊断专家团队的协作会诊过程，而非僵化的流程图。 |
| **状态管理** | 显式的全局共享状态对象 2 | 状态隐含于对话历史中，也可通过 save_state() 显式管理 10 | AutoGen 的对话历史提供了丰富的上下文，而显式状态持久化功能确保了长任务的可恢复性。 |
| **控制流** | 由预定义的图结构边决定，确定性高 2 | 由动态的发言人选择逻辑决定，可以是 LLM 驱动或自定义函数 11 | 通过实现基于 FSM 的自定义选择逻辑，可以在 AutoGen 中实现与 LangGraph 相媲美的确定性控制，同时保留对话的灵活性。 |
| **扩展性** | 通过添加或修改图节点和边进行扩展 | 通过定义新的 Agent 角色、技能和对话模式进行扩展 4 | AutoGen 的模块化 Agent 设计使得添加新的诊断专家或工具变得更加简单和独立。 |
| **易用性** | 学习曲线较陡，需要理解图论和状态机概念 1 | 对话范式更直观，但高级应用需要深入理解其内部机制 12 | 对于构建迭代式、适应性强的诊断流程，AutoGen 的心智模型更易于团队理解和维护。 |

---

## **第一部分：系统架构蓝图**

本节将详细阐述新系统的整体设计，包括其核心的五 Agent 团队、精心设计的控制流以及实现并行诊断的关键技术。

### **1.1. 五 Agent 诊断团队：角色与职责**

新架构的核心是一个由五个专业 Agent 组成的团队，每个 Agent 都被赋予了明确的角色和职责，以确保整个诊断流程的高效与精确。

1. **规划者 (Planner)**  
   * **类型**: autogen.AssistantAgent  
   * **职责**: 作为整个工作流的总设计师和协调者。它接收用户提出的初始问题（例如，“我的服务延迟很高”），并将其分解为一个高层次、结构化的行动计划。例如，计划可能包括：“1. 并行启动 k8s、sls 和 arms 诊断；2. 等待并整合所有诊断报告；3. 基于报告生成一个具体的执行方案；4. 将方案交由执行者处理。” 规划者的系统提示（system prompt）对于其任务分解和规划能力至关重要 13。  
2. **诊断者 (Diagnostic Agents)**  
   * **类型**: 三个独立的 autogen.AssistantAgent 实例，分别对应 Kubernetes (k8s)、日志服务 (SLS) 和应用实时监控服务 (ARMS)。  
   * **职责**: 这些是领域专家。每个诊断 Agent 都配备了专门用于其领域的工具集（通过函数调用或 MCP Workbench 实现）。它们的设计目标是“单一职责”，即只关注自己的领域。它们的系统提示会明确指示它们利用工具进行调查，并以结构化的格式（如 JSON）输出诊断报告。它们将并行运作，以最大限度地缩短问题分析时间。  
3. **执行者 (Executor)**  
   * **类型**: autogen.UserProxyAgent  
   * **职责**: 担任与外部世界交互的“手”。该 Agent 的核心任务是接收并执行由规划者最终确定的行动方案，例如执行一个 kubectl 命令、一个修复脚本或一次 API 调用。它被配置为在安全的环境中执行代码（code_execution_config），并精确地捕获执行结果，包括标准输出（stdout）、标准错误（stderr）和退出码。选择 UserProxyAgent 是因为它被设计用来代理用户执行代码或提供输入，非常适合这个角色 14。  
4. **验证与总结者 (Validator)**  
   * **类型**: autogen.AssistantAgent  
   * **职责**: 作为最终的质量保证和报告生成者。它接收所有诊断报告、执行者的行动结果，并与最初的用户问题进行比对。其核心任务是评估问题是否已得到解决，并生成一份全面的总结报告。为了确保输出的可靠性和一致性，该 Agent 将被配置为强制输出一个 Pydantic 定义的结构化数据对象 15。

### **1.2. 控制流：工程化一个状态驱动的群聊**

为了满足系统对稳定性的核心要求，我们必须摒弃 AutoGen GroupChat 中默认的 'auto' 或 'round_robin' 等简单的发言人选择策略 11。这些策略对于需要确定性流程的生产级应用来说过于天真和不可靠。

取而代之，我们将实现一种更先进的控制机制：在 GroupChat 之上覆盖一个有限状态机（FSM）。这种模式在 AutoGen 的官方文档中有所提及，它允许我们将工作流的各个阶段定义为不同的“状态” 6。在我们的系统中，这些状态将是：

PLANNING（规划中）、DIAGNOSING（诊断中）、EXECUTING（执行中）和 VALIDATING（验证中）。

我们将通过编写一个自定义的 speaker_selection_method 函数来实现这个状态机。这个函数将根据对话的当前状态（即上一个发言的 Agent）来决定下一个可以发言的 Agent 集合，从而严格控制流程的走向。其逻辑示例如下：

* **初始状态**: 对话由 Planner 发起，进入 PLANNING 状态。  
* **状态转移 PLANNING -> DIAGNOSING**: 当 Planner 完成规划并发出诊断指令后，自定义函数将只允许三位 Diagnostic Agents 发言。  
* **状态转移 DIAGNOSING -> EXECUTING**: 控制逻辑会等待，直到所有三个 Diagnostic Agents 都已提供其报告。一旦收集齐全，下一个合法的发言者将被指定为 Executor。  
* **状态转移 EXECUTING -> VALIDATING**: 当 Executor 完成任务并报告结果后，发言权将移交给 Validator。  
* **终止状态**: 当 Validator 完成最终报告后，它会发出一个特殊的 TERMINATE 消息，结束整个对话。

这种方法巧妙地结合了 LangGraph 的确定性控制流和 AutoGen 的对话上下文管理能力，实现了两者的优点。

### **1.3. 实现并发：并行诊断层**

并行执行三个诊断任务是本架构的关键性能要求。单纯的串行诊断会极大延长故障处理时间。我们将探讨并推荐一种架构上更优的并发实现模式。

* 方法一（简单但不推荐）：a_initiate_chats  
  一种直接的实现方式是在 Planner Agent 内部使用 asyncio.gather 来同时启动与三个诊断 Agent 的独立对话（通过 a_initiate_chats 方法）18。  
  Planner 会等待所有对话结束后，收集各自的结果。这种方法的缺点是 Planner 的逻辑会变得非常复杂和状态化，它需要管理多个并行的对话流、处理可能的超时或失败，并负责结果的合并。这增加了 Planner 的脆弱性，违反了单一职责原则。  
* 方法二（推荐的高级模式）：“并发 Agent” 事件驱动模式  
  为了构建一个更健壮、更解耦的系统，我们强烈推荐采用基于 autogen-core 的事件驱动模式 19。这种模式将 Agent 视为相互独立的微服务，通过消息总线进行通信，是实现真正稳定并发的关键。  
  1. **定义主题 (Topics)**: 我们将定义几个消息主题，例如 DIAGNOSTIC_REQUEST_TOPIC 用于分发诊断任务，DIAGNOSTIC_RESULT_TOPIC 用于收集诊断结果。  
  2. **发布任务 (Publish)**: Planner 的职责被大大简化。在完成规划后，它只需向 DIAGNOSTIC_REQUEST_TOPIC 发布一条包含问题描述和任务 ID 的消息。之后，它可以进入等待状态或处理其他事务。  
  3. **订阅与处理 (Subscribe & Process)**: 三个 Diagnostic Agents 将各自订阅 DIAGNOSTIC_REQUEST_TOPIC。当新消息到达时，它们会并发地被激活，独立地执行各自的诊断工具，然后将带有任务 ID 的诊断报告发布到 DIAGNOSTIC_RESULT_TOPIC。  
  4. **聚合结果 (Aggregate)**: 一个专门的聚合器（可以是 Executor Agent 的前置逻辑，或一个独立的 ClosureAgent）将订阅 DIAGNOSTIC_RESULT_TOPIC。它的逻辑很简单：为每个任务 ID 等待并收集三个诊断报告。一旦收集完毕，它就会触发工作流的下一步，即将整合后的信息传递给 Executor。

这种事件驱动的架构具有显著的优势。如果 K8sDiagnostician 在处理过程中失败或超时，它不会影响到 SLSDiagnostician 和 ARMSDiagnostician 的正常工作，也不会拖垮 Planner。聚合器可以实现更复杂的容错逻辑，例如“如果在5分钟内未收到所有三个报告，则上报诊断超时失败”。这种设计上的解耦和弹性，直接满足了用户对“稳定性”和“架构合理性”的核心要求。

---

## **第二部分：基础环境设置与配置**

在编写任何 Agent 代码之前，建立一个专业、可维护的项目环境至关重要。本节将指导您完成项目结构、依赖管理和 LLM 配置。

### **2.1. 建立项目环境**

为了确保项目的可扩展性和团队协作的便利性，我们推荐采用标准的 Python 项目布局。

* 目录结构  
  一个清晰的目录结构是良好软件工程的开端。推荐结构如下 20：  
  /autogen_diagnostic_system  
  ├── pyproject.toml         # 项目元数据和依赖管理  
  ├──.env                   # 存储环境变量和密钥  
  ├── README.md              # 项目说明  
  └── /src  
      ├── __init__.py  
      ├── main.py            # 系统主入口，启动 Agent 流程  
      ├── /agents            # 存放所有 Agent 的定义  
      │   ├── __init__.py  
      │   ├── planner.py  
      │   ├── diagnosticians.py  
      │   ├── executor.py  
      │   └── validator.py  
      ├── /core              # 核心逻辑与配置  
      │   ├── __init__.py  
      │   └── config.py      # 加载和管理配置  
      ├── /tools             # 自定义工具函数或 MCP 服务器实现  
      │   └── mcp_servers/  
      └── /utils             # 通用辅助函数  
  └── /tests                 # 单元测试和集成测试  
      └──...

* 依赖管理  
  为了实现可复现的构建和清晰的依赖关系，应使用 pyproject.toml 文件配合 Poetry 或 PDM 等现代化的包管理工具 20。这优于传统的  
  requirements.txt，因为它能更好地处理依赖解析、锁定版本和打包发布。  
* 配置与安全  
  严禁在代码中硬编码任何敏感信息，如 API 密钥或端点地址。所有配置都应通过环境变量来管理。我们将创建一个 src/core/config.py 模块，使用 python-dotenv 库从项目根目录的 .env 文件中加载配置 21。这种做法不仅安全，而且使得在不同环境（开发、测试、生产）之间切换配置变得轻而易举。

### **2.2. 集成 Qwen LLM**

根据要求，系统需要使用 Qwen 大语言模型。AutoGen 的 AssistantAgent 主要通过一个与 OpenAI API 兼容的客户端进行交互 23。因此，集成的关键在于将 Qwen 模型部署在一个能提供此类兼容接口的服务之后。这可以通过 vLLM、FastChat 或其他模型服务框架来实现。

一旦该服务部署完成，在 AutoGen 中配置它就非常简单。以下是在 src/core/config.py 中配置 llm_config 的完整示例代码，它从 .env 文件中读取必要的参数 23：

Python

# 在 src/core/config.py 中  
import os  
from dotenv import load_dotenv

# 加载.env 文件中的环境变量  
load_dotenv()

# 从环境变量获取 Qwen 服务的配置  
QWEN_API_KEY = os.getenv("QWEN_API_KEY", "EMPTY")  # API 密钥，如果服务需要的话  
QWEN_BASE_URL = os.getenv("QWEN_BASE_URL")       # 例如 "http://localhost:8000/v1"  
QWEN_MODEL_NAME = os.getenv("QWEN_MODEL_NAME", "qwen-72b-chat") # 服务端期望的模型名

# 检查必要的配置是否存在  
if not QWEN_BASE_URL:  
    raise ValueError("QWEN_BASE_URL must be set in the.env file.")

# 为 AutoGen Agent 定义 LLM 配置字典  
llm_config = {  
    "config_list":,  
    "temperature": 0.1,  # 对于规划和诊断任务，使用较低的温度以获得更确定性的输出  
    "timeout": 120,      # 设置 API 请求超时时间为 120 秒  
}

值得注意的是，引入 Qwen 模型不仅仅是修改一个配置字典。它为整个系统引入了一个新的基础设施依赖——即那个提供 OpenAI 兼容 API 的模型服务。这个服务的稳定性、性能和资源消耗（如 GPU 显存）现在直接关系到整个诊断系统的稳定性。因此，在开发和运维过程中，必须将这个模型服务作为一个关键组件进行监控和管理，确保其高可用性。

---

## **第三部分：核心实现：构建 Agent 团队**

本节将深入代码层面，提供构建五个核心 Agent 的详细实现，包括它们的初始化、系统提示以及工具集成。

### **3.1. 规划者 Agent (src/agents/planner.py)**

Planner 是整个流程的大脑。其实现的关键在于一个精心设计的系统提示，引导它进行有效的任务分解。

Python

# src/agents/planner.py  
import autogen  
from..core.config import llm_config

PLANNER_SYSTEM_MESSAGE = """  
You are a master IT operations planner. Your role is to receive a user-reported problem and create a clear, high-level, step-by-step plan to diagnose and resolve it.

Your plan must follow these steps:  
1.  **Analyze the Request**: Briefly state your understanding of the user's problem.  
2.  **Formulate Diagnostic Plan**: Announce that you will initiate parallel diagnostics across three domains: Kubernetes (k8s), Logging (sls), and Application Monitoring (arms).  
3.  **Define Execution Goal**: Based on the problem description, define a clear success criterion. For example, "The goal is to reduce the p99 latency of the 'checkout-service' to below 200ms."  
4.  **Initiate Diagnosis**: Output a concise instruction for the diagnostic team to start their work, including the user's original problem description. Your message should trigger the diagnostic agents.

Do not attempt to solve the problem yourself. Your sole responsibility is to create the initial plan and kick off the diagnostic process.  
"""

def get_planner_agent() -> autogen.AssistantAgent:  
    """Initializes and returns the Planner agent."""  
    return autogen.AssistantAgent(  
        name="Planner",  
        llm_config=llm_config,  
        system_message=PLANNER_SYSTEM_MESSAGE,  
    )

### **3.2. 诊断者 Agent 与外部工具 (src/agents/diagnosticians.py)**

诊断者是系统的专家，它们需要与外部工具交互来获取数据。这里我们将探讨两种实现外部工具集成的方法，并给出明确的建议。

#### **3.2.1. 方法一（实验性）：通过 MCP Workbench 集成**

用户的请求提到了使用 McpWorkbench 通过 URL 提供工具。模型上下文协议（MCP）是一个旨在将工具作为独立服务提供给 Agent 的先进协议 26。理论上，这能实现工具与 Agent 的完全解耦。

实现方式如下：首先，需要为 k8s、sls、arms 的诊断功能分别启动三个独立的工具服务器，每个服务器都在一个 URL 端点上（例如，http://k8s-tool-server:8081）遵循 MCP 规范提供工具。然后，在 AutoGen 中，可以这样配置 McpWorkbench 来连接这些服务 27：

Python

# 概念性代码示例  
from autogen_ext.tools.mcp import McpWorkbench, SseServerParams  
import autogen

# 假设 k8s_tool_server_url 是从配置中读取的  
k8s_tool_server_params = SseServerParams(url=k8s_tool_server_url)  
k8s_workbench = McpWorkbench(server_params=k8s_tool_server_params)

# 创建诊断 Agent 时传入 workbench  
k8s_diagnostician = autogen.AssistantAgent(  
    name="K8s_Diagnostician",  
    llm_config=llm_config,  
    system_message="...",  
    # 注意：截至最新版本，AssistantAgent 可能没有直接的 workbench 参数，  
    # 集成可能需要更底层的 Agent 类型或自定义实现。  
    # 此处为示意。  
)

#### **3.2.2. 方法二（生产级推荐）：标准函数调用**

**一个至关重要的提醒**：尽管 MCP 是一个富有远见的协议，但根据社区和专家的反馈，其当前的实现可能还不够成熟和稳定，有时会存在一些难以排查的错误 29。对于一个将“稳定性”放在首位的生产系统，我们

**强烈推荐**使用 AutoGen 内置的、经过充分测试的**函数调用（Function Calling）**机制。

这种方法同样可以实现调用位于外部 URL 的工具，但它利用的是 AutoGen 最稳定可靠的特性。实现方式是，在 Agent 的代码中定义一个普通的 Python 函数，该函数内部再通过 requests 或 httpx 等库来调用外部的工具 URL。

#### **3.2.3. 诊断者 Agent 代码实现（采用函数调用）**

下面是 K8sDiagnostician 的完整实现，它使用推荐的函数调用方法。SLSDiagnostician 和 ARMSDiagnostician 的实现将遵循完全相同的模式。

Python

# src/agents/diagnosticians.py  
import autogen  
import requests  
import json  
from typing import Annotated  
from..core.config import llm_config

# 假设工具服务器的地址在配置中  
K8S_TOOL_SERVER_URL = "http://k8s-tool-server:8081/diagnose"

DIAGNOSTICIAN_SYSTEM_MESSAGE = """  
You are a specialized {domain} diagnostic expert. Your only task is to use the provided tools to investigate the user's problem within your domain.  
You must:  
1. Call the `diagnose_problem` function with the user's problem description.  
2. Receive the JSON output from the tool.  
3. Format the result into a clear, structured JSON report. Your final output must be a single JSON object containing the keys "domain", "findings", and "raw_data".  
4. Do not interpret the findings or suggest solutions. Simply report the data.  
"""

class DiagnosticTool:  
    def __init__(self, domain: str, tool_url: str):  
        self.domain = domain  
        self.tool_url = tool_url

    def diagnose_problem(  
        self,  
        problem_description: Annotated  
    ) -> str:  
        """Calls the external diagnostic tool server to get data."""  
        try:  
            response = requests.post(self.tool_url, json={"problem": problem_description}, timeout=60)  
            response.raise_for_status()  
            # 返回 JSON 字符串，让 LLM 进行处理和总结  
            return json.dumps(response.json())  
        except requests.RequestException as e:  
            return json.dumps({"error": f"Failed to call diagnostic tool for {self.domain}: {str(e)}"})

def get_k8s_diagnostician() -> autogen.AssistantAgent:  
    """Initializes and returns the K8s Diagnostician agent with its tool."""  
    k8s_tool = DiagnosticTool(domain="Kubernetes", tool_url=K8S_TOOL_SERVER_URL)  
      
    k8s_agent = autogen.AssistantAgent(  
        name="K8s_Diagnostician",  
        llm_config=llm_config,  
        system_message=DIAGNOSTICIAN_SYSTEM_MESSAGE.format(domain="Kubernetes (k8s)"),  
    )  
      
    # 将工具函数注册给 Agent  
    k8s_agent.register_for_llm(name="diagnose_problem", description="Run a diagnostic check for the given problem.")(k8s_tool.diagnose_problem)  
      
    return k8s_agent

# 类似地，可以创建 get_sls_diagnostician 和 get_arms_diagnostician

### **3.3. 执行者 Agent (src/agents/executor.py)**

Executor Agent 的关键在于安全地执行代码。我们将其配置为 UserProxyAgent，并强烈建议在 Docker 容器中执行代码，以隔离环境并防止意外的系统更改 30。

Python

# src/agents/executor.py  
import autogen  
from..core.config import llm_config

EXECUTOR_SYSTEM_MESSAGE = """  
You are an automated code execution engine. You will be given a shell command or a Python script to execute.  
You must:  
1. Execute the code exactly as provided in a secure environment.  
2. Do not modify the code or ask for confirmation.  
3. Report back the full, raw output, including stdout, stderr, and the exit code.  
4. If the code executes successfully, reply with the results and the word "TERMINATE".  
5. If the code fails, reply with the error details and the word "TERMINATE".  
"""

def get_executor_agent() -> autogen.UserProxyAgent:  
    """Initializes and returns the Executor agent."""  
    return autogen.UserProxyAgent(  
        name="Executor",  
        human_input_mode="NEVER",  
        system_message=EXECUTOR_SYSTEM_MESSAGE,  
        code_execution_config={  
            "work_dir": "execution_context",  
            "use_docker": True,  # 强烈建议在生产环境中使用 Docker  
        },  
    )

### **3.4. 验证与总结者 Agent (src/agents/validator.py)**

Validator Agent 的核心是生成可靠、一致的最终报告。我们将使用 Pydantic 来定义报告的结构，并强制 LLM 按此结构输出，这极大地提升了系统的可靠性 15。

Python

# src/agents/validator.py  
import autogen  
from pydantic import BaseModel, Field  
from typing import Literal  
from..core.config import llm_config

class FinalReport(BaseModel):  
    """A structured model for the final diagnostic and resolution report."""  
    is_resolved: bool = Field(..., description="Was the initial problem successfully resolved?")  
    confidence_score: float = Field(..., ge=0.0, le=1.0, description="Confidence in the resolution, from 0.0 to 1.0.")  
    summary: str = Field(..., description="A concise summary of the initial problem, actions taken, and the final outcome.")  
    reasoning: str = Field(..., description="Step-by-step reasoning for the is_resolved and confidence_score conclusions, based on diagnostic data and execution results.")

VALIDATOR_SYSTEM_MESSAGE = """  
You are a senior Site Reliability Engineer (SRE) responsible for final validation.  
You will receive the initial user problem, a collection of diagnostic reports, and the result of an execution action.  
Your task is to synthesize all this information and produce a final, structured report using the `FinalReport` tool.

You must:  
1.  **Compare**: Compare the execution result against the diagnostic findings and the initial problem statement.  
2.  **Conclude**: Determine if the action taken has resolved the issue.  
3.  **Justify**: Provide clear reasoning for your conclusion.  
4.  **Report**: Call the `FinalReport` tool with your complete analysis. This is your only allowed action.  
"""

def get_validator_agent() -> autogen.AssistantAgent:  
    """Initializes and returns the Validator agent configured for structured output."""  
    # 在 llm_config 中为该 Agent 专门配置 tools，以强制其使用 Pydantic 模型  
    validator_llm_config = llm_config.copy()  
    validator_llm_config["tools"] =  
      
    return autogen.AssistantAgent(  
        name="Validator",  
        llm_config=validator_llm_config,  
        system_message=VALIDATOR_SYSTEM_MESSAGE,  
    )

通过这种方式，我们不仅构建了一个分工明确的 Agent 团队，还为关键的交互环节（工具使用和最终输出）建立了健壮的、基于数据契约的通信机制。

---

## **第四部分：生产级稳定性的高级技术**

将一个多 Agent 系统从原型推向生产，需要实施一系列旨在增强其可靠性、可维护性和可观测性的高级技术。本节将详细介绍如何为我们的诊断系统构建这些关键的保障措施。

### **4.1. 确保鲁棒性：错误处理、超时与持久化**

生产环境充满了不确定性，从网络抖动到服务过载。一个健壮的系统必须能够优雅地处理这些异常情况。

* 模型层面的弹性  
  对 LLM 的 API 调用是系统中最常见的外部依赖。我们可以通过在 llm_config 中配置 timeout 和 max_retries 参数来增加其弹性。timeout 防止单次请求无限期等待，而 max_retries 则允许在遇到可重试的错误（如 API 速率限制或瞬时网络问题）时自动重试 31。

  此外，AutoGen 的 config_list 支持配置多个模型端点。如果列表中的第一个端点失败，AutoGen 会自动尝试列表中的下一个端点，这为模型服务的高可用性提供了一个简单的故障转移机制 32。  
  Python  
  # 在 core/config.py 中增强 llm_config  
  llm_config = {  
      "config_list":,  
      "temperature": 0.1,  
      "timeout": 120,      # 请求超时  
      "max_retries": 3,    # 最大重试次数  
  }

* Agent 层面的错误处理  
  更复杂的错误，例如一个 Agent 的逻辑失败，需要在工作流层面处理。这需要 Planner 或一个专门的协调者具备识别和响应失败的能力。一个有效的模式是，让 Agent 在其输出中明确地标识成功或失败。例如，如果 Executor Agent 执行命令失败，它返回的消息应包含 "Error: command failed" 等关键词。  
  Planner Agent 的系统提示可以被增强，以包含处理这种情况的指令：“如果一个步骤失败，请分析失败原因，并尝试提出一个替代方案或‘变通方法’（workaround）” 13。这使得 Agent 团队具备了一定程度的自我修复能力。  
* 状态持久化与可恢复性  
  对于可能长时间运行的诊断任务，防止因意外中断（如进程崩溃）而丢失所有进展至关重要。AutoGen 提供了 save_state() 和 load_state() 方法来实现状态的持久化 10。

  我们可以在工作流的关键节点（例如，在每个状态转换后）调用 groupchat.save_state()，并将返回的状态字典序列化为 JSON 文件并存储。如果流程中断，可以编写一个恢复脚本，该脚本读取最新的状态文件，创建一个新的 GroupChatManager 实例，并调用 load_state() 来从中断点继续执行 10。这对于确保长任务的最终完成至关重要。  
  Python  
  # 在 main.py 的主循环中  
  import json

  #... 在 GroupChatManager 执行一步后...  
  state = await manager.save_state()  
  with open("workflow_state.json", "w") as f:  
      json.dump(state, f)

### **4.2. 管理数据完整性：使用 Pydantic 进行结构化数据交换**

Agent 之间仅通过自然语言文本进行通信是脆弱的。一个 Agent 的输出可能格式多变，导致接收方 Agent 难以解析，甚至产生误解。为了根除这类问题，我们应在所有关键的数据交换环节强制使用结构化数据。

Pydantic 模型是定义这种数据契约的理想工具。除了在 Validator 中使用外，我们还应该为其他核心数据对象定义 Pydantic 模型，例如：

Python

# 在一个新文件 src/core/models.py 中  
from pydantic import BaseModel, Field  
from typing import List, Dict, Any

class DiagnosticReport(BaseModel):  
    domain: str = Field(..., description="The domain of the diagnosis (e.g., 'k8s', 'sls').")  
    findings: List[str] = Field(..., description="A list of key findings from the diagnosis.")  
    raw_data: Dict[str, Any] = Field(..., description="The raw data returned by the diagnostic tool.")

class ExecutionPlan(BaseModel):  
    command_type: Literal["shell", "python"] = Field(..., description="The type of command to execute.")  
    command: str = Field(..., description="The command or script to be executed.")  
    justification: str = Field(..., description="Why this command is expected to solve the problem.")

然后，我们可以配置相应的 Agent（Diagnostic Agents 和 Planner）来强制输出这些模型。这种做法的好处是多方面的 15：

1. **减少幻觉**：通过提供一个严格的模式，限制了 LLM 输出的自由度，使其更专注于生成格式正确的数据。  
2. **提高安全性**：当一个 Agent 期望接收一个 JSON 对象时，它对隐藏在看似普通文本中的指令性攻击（Prompt Injection）的抵抗力更强。  
3. **简化代码**：下游 Agent 可以直接使用干净的 Python 对象，无需编写复杂且易错的正则表达式或字符串解析逻辑来从文本中提取信息。

### **4.3. 可观测性：调试与日志记录**

当多 Agent 系统行为不符合预期时，如果没有充分的日志，调试将是一场噩梦。我们需要建立一个分层的日志系统来提供全面的可观测性。

* 追踪日志 (Trace Logging)  
  为了看到 Agent 的“内心独白”和详细的操作步骤，我们可以启用 AutoGen 的追踪日志。这通过配置 Python 的标准 logging 模块并设置 autogen_agentchat.TRACE_LOGGER_NAME 的级别来实现 34。这些日志是为开发人员调试而设计的，内容详细但格式不保证稳定。  
* 结构化事件日志 (Structured Event Logging)  
  为了进行程序化的分析和监控，我们应该使用结构化日志。AutoGen 通过 autogen_core.EVENT_LOGGER_NAME 发出结构化事件，例如 LLMCallEvent 35。我们可以编写一个自定义的  
  logging.Handler 来捕获这些事件，并从中提取关键指标，如每次 LLM 调用的 token 消耗、成本、延迟等 36。这对于性能分析和成本控制至关重要。  
* 持久化运行时日志 (Persistent Runtime Logging)  
  AutoGen 提供了一个非常强大的功能 autogen.runtime_logging，可以将整个对话的完整记录（包括谁对谁说了什么，调用了什么函数，返回了什么结果）持久化到一个 SQLite 数据库中 37。只需在应用启动时调用  
  autogen.runtime_logging.start()，就可以为每次运行创建一个可追溯的、完整的审计日志。这对于事后分析复杂的、非预期的 Agent 交互行为是无价的。

Python

# 在 main.py 的开头  
import logging  
import autogen

# 配置追踪日志  
logging.basicConfig(level=logging.WARNING)  
trace_logger = logging.getLogger(autogen.agentchat.TRACE_LOGGER_NAME)  
trace_logger.setLevel(logging.INFO)  
trace_logger.addHandler(logging.StreamHandler())

# 启动运行时日志记录到数据库  
autogen.runtime_logging.start(config={"dbname": "logs.db"})

#... 运行主程序...

# 在程序结束时停止日志记录  
autogen.runtime_logging.stop()

将这些稳定性技术结合起来，就形成了一个从底层 API 调用到高层工作流逻辑，再到数据交换和事后分析的全方位保障体系。它们不是孤立的功能，而是一个协同工作的系统，共同将一个实验性的 Agent 原型提升为可信赖的生产级应用。

---

## **结论：一个可扩展的 Agentic 系统蓝图**

本指南详细阐述了如何使用 Microsoft AutoGen 框架，将一个基于 LangGraph 的复杂编排系统重构为一个健壮、高效且稳定的多 Agent 诊断系统。我们不仅提供了详细的实现代码，更重要的是，阐明了背后关键的架构决策及其理由。

### **架构决策总结**

* **状态驱动的群聊 (State-Driven Group Chat)**: 我们通过自定义发言人选择函数，在 AutoGen 灵活的对话模型之上实现了一个确定性的有限状态机。这兼具了 LangGraph 的可控性和 AutoGen 的动态性，是整个架构稳定运行的基石。  
* **事件驱动的并发模型 (Concurrent Agents Pattern)**: 针对并行诊断的需求，我们推荐了基于 autogen-core 的事件驱动模式。这种模式通过消息主题实现 Agent 间的解耦，相比简单的多线程调用，其可扩展性和容错性都更胜一筹。  
* **稳健的工具集成 (Standard Function Calling)**: 尽管 McpWorkbench 是一个前瞻性的功能，但我们基于对生产稳定性的考量，强烈推荐使用 AutoGen 成熟的内置函数调用机制来集成外部工具。这是一种务实且风险更低的选择。  
* **强制的结构化数据 (Pervasive Pydantic Models)**: 我们提倡在所有关键的 Agent 间通信中使用 Pydantic 模型定义数据契约。这从根本上消除了因解析非结构化文本而引入的模糊性和错误，是提升系统可靠性的最有效手段之一。

这些决策共同作用，构建了一个满足用户所有要求的系统：它拥有一个由规划者、并行诊断者、执行者和验证者组成的合理架构；通过 OpenAI 兼容的接口成功集成了 Qwen 模型；并且通过一系列高级技术保证了其稳定性和代码简洁性。

### **未来增强与可扩展性展望**

这个架构蓝图也为未来的发展奠定了坚实的基础：

* **人机协作 (Human-in-the-Loop)**: 当前系统是全自动的。但在关键决策点，例如在 Executor 执行一个高风险命令之前，可以轻松地引入人工审批环节。只需将 Executor Agent 的 human_input_mode 参数设置为 "ALWAYS"，系统就会在该步骤暂停并等待人类的确认 38。  
* **全面的测试策略 (Testing Strategy)**: 为了保证系统的长期质量，应建立一套完整的测试体系。使用 pytest 框架，可以对每个 Agent 的逻辑进行单元测试（通过模拟 LLM 响应和工具输出），并对整个工作流进行集成测试 40。此外，可以利用  
  AutoGenBench 框架对系统的端到端性能进行基准测试和评估 41。  
* **分布式扩展 (Scaling the System)**: 当前架构中的 Agent 都在同一个进程中运行。当业务规模扩大时，事件驱动的设计使其能够平滑地扩展到分布式环境。可以利用 AutoGen 的 GrpcWorkerAgentRuntime 等组件，将每个 Agent 或每组 Agent部署为独立的服务（例如在 Kubernetes Pod 中），它们通过 gRPC 进行通信，从而实现水平扩展和更高的吞吐量 42。

总之，本指南提供的不只是一个特定问题的解决方案，更是一套在复杂、动态的环境中设计、构建和维护生产级多 Agent 系统的思想和方法。通过遵循这些原则，开发团队可以充满信心地构建出能够自主解决问题、并能随着需求增长而不断演进的下一代 AI 应用。

---

## **附录：完整系统代码示例**

为了将所有概念整合在一起，下面提供一个简化的、可运行的 main.py 脚本。请注意，此脚本为演示目的进行了简化，并假设外部工具服务器已在本地运行。

Python

import asyncio  
import autogen  
import json  
import logging  
from typing import List, Dict, Any, Literal  
from pydantic import BaseModel, Field

# --- 1. 配置 ---  
# 强烈建议从.env 文件加载这些值  
QWEN_BASE_URL = "http://localhost:8000/v1"  
QWEN_MODEL_NAME = "qwen-72b-chat"  
K8S_TOOL_URL = "http://localhost:8081/diagnose"  
SLS_TOOL_URL = "http://localhost:8082/diagnose"  
ARMS_TOOL_URL = "http://localhost:8083/diagnose"

llm_config = {  
    "config_list":,  
    "temperature": 0.1,  
    "timeout": 120,  
}

# --- 2. Pydantic 模型定义 ---  
class FinalReport(BaseModel):  
    is_resolved: bool = Field(...)  
    summary: str = Field(...)  
    reasoning: str = Field(...)

# --- 3. 工具定义 ---  
def make_diagnostic_tool(domain: str, url: str):  
    def diagnose_problem(problem_description: str) -> str:  
        try:  
            response = requests.post(url, json={"problem": problem_description}, timeout=60)  
            response.raise_for_status()  
            return json.dumps(response.json())  
        except Exception as e:  
            return json.dumps({"error": str(e)})  
    return diagnose_problem

# --- 4. Agent 定义 ---  
# Planner  
planner = autogen.AssistantAgent(  
    name="Planner",  
    llm_config=llm_config,  
    system_message="You are a master planner. Receive a problem, create a plan to use k8s, sls, and arms diagnostics, then instruct the team to start."  
)

# Diagnosticians  
k8s_diagnostician = autogen.AssistantAgent(  
    name="K8s_Diagnostician",  
    llm_config=llm_config,  
    system_message="You are a k8s expert. Use the `diagnose_problem` tool and report findings as JSON.",  
)  
k8s_diagnostician.register_for_llm(name="diagnose_problem", description="Run k8s diagnostics.")(make_diagnostic_tool("k8s", K8S_TOOL_URL))

sls_diagnostician = autogen.AssistantAgent(name="SLS_Diagnostician", llm_config=llm_config, system_message="You are an sls expert. Use the `diagnose_problem` tool and report findings as JSON.")  
sls_diagnostician.register_for_llm(name="diagnose_problem", description="Run sls diagnostics.")(make_diagnostic_tool("sls", SLS_TOOL_URL))

arms_diagnostician = autogen.AssistantAgent(name="ARMS_Diagnostician", llm_config=llm_config, system_message="You are an arms expert. Use the `diagnose_problem` tool and report findings as JSON.")  
arms_diagnostician.register_for_llm(name="diagnose_problem", description="Run arms diagnostics.")(make_diagnostic_tool("arms", ARMS_TOOL_URL))

# Executor  
executor = autogen.UserProxyAgent(  
    name="Executor",  
    human_input_mode="NEVER",  
    code_execution_config={"work_dir": "execution_context", "use_docker": False}, # 在本地测试时可设为 False  
    system_message="Execute the given shell command and report the result."  
)

# Validator  
validator_llm_config = llm_config.copy()  
validator_llm_config["tools"] =  
validator = autogen.AssistantAgent(  
    name="Validator",  
    llm_config=validator_llm_config,  
    system_message="You are a senior SRE. Synthesize all information and create a final report using the FinalReport tool."  
)

# --- 5. 状态驱动的群聊控制 ---  
agents = [planner, k8s_diagnostician, sls_diagnostician, arms_diagnostician, executor, validator]  
diagnostic_agents = [k8s_diagnostician, sls_diagnostician, arms_diagnostician]  
diagnostic_reports = {}

def custom_speaker_selection_func(last_speaker: autogen.Agent, groupchat: autogen.GroupChat) -> autogen.Agent:  
    messages = groupchat.messages  
      
    if last_speaker is planner:  
        # Planner 说话后，轮到所有诊断者  
        # 这里用轮询模拟并行，实际应使用事件驱动模式  
        return k8s_diagnostician  
      
    if last_speaker in diagnostic_agents:  
        # 记录报告  
        diagnostic_reports[last_speaker.name] = messages[-1]['content']  
        # 检查是否所有诊断都已完成  
        if len(diagnostic_reports) == 3:  
            return executor # 所有诊断完成，轮到执行者  
        else:  
            # 轮到下一个诊断者  
            current_index = diagnostic_agents.index(last_speaker)  
            next_index = (current_index + 1) % len(diagnostic_agents)  
            return diagnostic_agents[next_index]

    if last_speaker is executor:  
        return validator

    # 默认或初始情况  
    return planner

# --- 6. 主程序 ---  
async def main():  
    # 配置日志  
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(message)s')  
      
    # 创建群聊  
    groupchat = autogen.GroupChat(  
        agents=agents,  
        messages=,  
        max_round=15,  
        speaker_selection_method=custom_speaker_selection_func  
    )  
    manager = autogen.GroupChatManager(groupchat=groupchat, llm_config=llm_config)

    # 启动流程的用户代理  
    user_proxy = autogen.UserProxyAgent(  
        name="User_Proxy",  
        human_input_mode="NEVER",  
        code_execution_config=False,  
        is_termination_msg=lambda x: "FINAL REPORT" in x.get("content", "").upper() or "TERMINATE" in x.get("content", "").upper()  
    )

    initial_problem = "The 'checkout-service' in the 'prod' namespace is experiencing high latency and occasional 503 errors."  
      
    # 准备传递给诊断者的消息  
    # 在真实的状态机中，这个消息的构建会更复杂  
    combined_diagnostics_message = f"""  
    Based on the initial plan, please perform your diagnostics.  
    The problem is: {initial_problem}  
    After all diagnostics are complete, the Executor will receive a consolidated plan.  
    """  
      
    # 启动对话  
    await user_proxy.a_initiate_chat(  
        manager,  
        message=initial_problem  
    )

if __name__ == "__main__":  
    # 假设外部工具服务已启动  
    # 在此运行 main 函数  
    # 注意：由于使用了 a_initiate_chat, 需要在异步上下文中运行  
    # 例如：  
    try:  
        asyncio.run(main())  
    except KeyboardInterrupt:  
        print("Process interrupted.")

#### **Works cited**

1. LangGraph vs AutoGen: Comparing AI Agent Frameworks - PromptLayer, accessed July 31, 2025, [https://blog.promptlayer.com/langgraph-vs-autogen/](https://blog.promptlayer.com/langgraph-vs-autogen/)  
2. Technical Comparison of AutoGen, CrewAI, LangGraph, and ..., accessed July 31, 2025, [https://ai.plainenglish.io/technical-comparison-of-autogen-crewai-langgraph-and-openai-swarm-1e4e9571d725](https://ai.plainenglish.io/technical-comparison-of-autogen-crewai-langgraph-and-openai-swarm-1e4e9571d725)  
3. Exploring Microsoft's AutoGen Framework for Agentic Workflow - Analytics Vidhya, accessed July 31, 2025, [https://www.analyticsvidhya.com/blog/2024/07/microsofts-autogen-framework-for-agentic-workflow/](https://www.analyticsvidhya.com/blog/2024/07/microsofts-autogen-framework-for-agentic-workflow/)  
4. AutoGen vs. LangGraph vs. CrewAI:Who Wins? | by Khushbu Shah | ProjectPro - Medium, accessed July 31, 2025, [https://medium.com/projectpro/autogen-vs-langgraph-vs-crewai-who-wins-02e6cc7c5cb8](https://medium.com/projectpro/autogen-vs-langgraph-vs-crewai-who-wins-02e6cc7c5cb8)  
5. Autogen vs. LangGraph : r/LangChain - Reddit, accessed July 31, 2025, [https://www.reddit.com/r/LangChain/comments/1b7q44y/autogen_vs_langgraph/](https://www.reddit.com/r/LangChain/comments/1b7q44y/autogen_vs_langgraph/)  
6. FSM Group Chat -- User-specified agent transitions | AutoGen 0.2 - Microsoft Open Source, accessed July 31, 2025, [https://microsoft.github.io/autogen/0.2/blog/2024/02/11/FSM-GroupChat/](https://microsoft.github.io/autogen/0.2/blog/2024/02/11/FSM-GroupChat/)  
7. StateFlow: Build Workflows through State-Oriented Actions | AutoGen 0.2, accessed July 31, 2025, [https://microsoft.github.io/autogen/0.2/docs/notebooks/agentchat_groupchat_stateflow/](https://microsoft.github.io/autogen/0.2/docs/notebooks/agentchat_groupchat_stateflow/)  
8. Exploring Multi-Agent Conversation Patterns with AutoGen Framework | by Senol Isci, PhD, accessed July 31, 2025, [https://medium.com/@senol.isci/exploring-multi-agent-conversation-patterns-with-the-autogen-framework-29946f199ca5](https://medium.com/@senol.isci/exploring-multi-agent-conversation-patterns-with-the-autogen-framework-29946f199ca5)  
9. A practical guide for using AutoGen in software applications | by Clint Goodman - Medium, accessed July 31, 2025, [https://clintgoodman27.medium.com/a-practical-guide-for-using-autogen-in-software-applications-8799185d27ee](https://clintgoodman27.medium.com/a-practical-guide-for-using-autogen-in-software-applications-8799185d27ee)  
10. Managing State — AutoGen - Microsoft Open Source, accessed July 31, 2025, [https://microsoft.github.io/autogen/stable//user-guide/agentchat-user-guide/tutorial/state.html](https://microsoft.github.io/autogen/stable//user-guide/agentchat-user-guide/tutorial/state.html)  
11. AutoGen Conversation Patterns - Overview for Beginners - Getting Started with Artificial Intelligence, accessed July 31, 2025, [https://www.gettingstarted.ai/autogen-conversation-patterns-workflows/](https://www.gettingstarted.ai/autogen-conversation-patterns-workflows/)  
12. OpenAI Agents SDK vs LangGraph vs Autogen vs CrewAI - Composio, accessed July 31, 2025, [https://composio.dev/blog/openai-agents-sdk-vs-langgraph-vs-autogen-vs-crewai](https://composio.dev/blog/openai-agents-sdk-vs-langgraph-vs-autogen-vs-crewai)  
13. Design Patterns for AI Agents: Using Autogen for Effective Multi ..., accessed July 31, 2025, [https://medium.com/@LakshmiNarayana_U/design-patterns-for-ai-agents-using-autogen-for-effective-multi-agent-collaboration-5f1067a7c63b](https://medium.com/@LakshmiNarayana_U/design-patterns-for-ai-agents-using-autogen-for-effective-multi-agent-collaboration-5f1067a7c63b)  
14. Multi-agent Conversation Framework | AutoGen 0.2, accessed July 31, 2025, [https://microsoft.github.io/autogen/0.2/docs/Use-Cases/agent_chat/](https://microsoft.github.io/autogen/0.2/docs/Use-Cases/agent_chat/)  
15. Output - Pydantic AI, accessed July 31, 2025, [https://ai.pydantic.dev/output/](https://ai.pydantic.dev/output/)  
16. Autogen Tutorial: Build 100% Reliable AI Agents with Structured Output (Code Revealed), accessed July 31, 2025, [https://www.youtube.com/watch?v=7_HB90JhKJg](https://www.youtube.com/watch?v=7_HB90JhKJg)  
17. AutoGen - GroupChat with RAG - Kaggle, accessed July 31, 2025, [https://www.kaggle.com/code/anandsiva/autogen-groupchat-with-rag](https://www.kaggle.com/code/anandsiva/autogen-groupchat-with-rag)  
18. Solving Multiple Tasks in a Sequence of Async Chats | AutoGen 0.2 - Microsoft Open Source, accessed July 31, 2025, [https://microsoft.github.io/autogen/0.2/docs/notebooks/agentchat_multi_task_async_chats/](https://microsoft.github.io/autogen/0.2/docs/notebooks/agentchat_multi_task_async_chats/)  
19. Concurrent Agents — AutoGen - Microsoft Open Source, accessed July 31, 2025, [https://microsoft.github.io/autogen/stable//user-guide/core-user-guide/design-patterns/concurrent-agents.html](https://microsoft.github.io/autogen/stable//user-guide/core-user-guide/design-patterns/concurrent-agents.html)  
20. Project structure best practices : r/learnpython - Reddit, accessed July 31, 2025, [https://www.reddit.com/r/learnpython/comments/1ad36wi/project_structure_best_practices/](https://www.reddit.com/r/learnpython/comments/1ad36wi/project_structure_best_practices/)  
21. Building AI Agent Applications Series - Using AutoGen to build your AI Agents, accessed July 31, 2025, [https://techcommunity.microsoft.com/blog/educatordeveloperblog/building-ai-agent-applications-series---using-autogen-to-build-your-ai-agents/4052280](https://techcommunity.microsoft.com/blog/educatordeveloperblog/building-ai-agent-applications-series---using-autogen-to-build-your-ai-agents/4052280)  
22. How to Build an LLM Agent With AutoGen: Step-by-Step Guide, accessed July 31, 2025, [https://neptune.ai/blog/building-llm-agents-with-autogen](https://neptune.ai/blog/building-llm-agents-with-autogen)  
23. Non-OpenAI Models | AutoGen 0.2 - Microsoft Open Source, accessed July 31, 2025, [https://microsoft.github.io/autogen/0.2/docs/topics/non-openai-models/about-using-nonopenai-models/](https://microsoft.github.io/autogen/0.2/docs/topics/non-openai-models/about-using-nonopenai-models/)  
24. Models — AutoGen - Microsoft Open Source, accessed July 31, 2025, [https://microsoft.github.io/autogen/stable//user-guide/agentchat-user-guide/tutorial/models.html](https://microsoft.github.io/autogen/stable//user-guide/agentchat-user-guide/tutorial/models.html)  
25. How To Use AutoGen With ANY Open-Source LLM for FREE | by ai-for-devs.com | Medium, accessed July 31, 2025, [https://medium.com/@ai-for-devs.com/how-to-use-autogen-with-any-open-source-llm-for-free-5607f2bad2f4](https://medium.com/@ai-for-devs.com/how-to-use-autogen-with-any-open-source-llm-for-free-5607f2bad2f4)  
26. Workbench (and MCP) — AutoGen - Microsoft Open Source, accessed July 31, 2025, [https://microsoft.github.io/autogen/stable//user-guide/core-user-guide/components/workbench.html](https://microsoft.github.io/autogen/stable//user-guide/core-user-guide/components/workbench.html)  
27. Agents — AutoGen - Microsoft Open Source, accessed July 31, 2025, [https://microsoft.github.io/autogen/stable//user-guide/agentchat-user-guide/tutorial/agents.html](https://microsoft.github.io/autogen/stable//user-guide/agentchat-user-guide/tutorial/agents.html)  
28. autogen_ext.tools.mcp — AutoGen - Microsoft Open Source, accessed July 31, 2025, [https://microsoft.github.io/autogen/stable//reference/python/autogen_ext.tools.mcp.html](https://microsoft.github.io/autogen/stable//reference/python/autogen_ext.tools.mcp.html)  
29. MCPs with AutoGen Agents (with any Local LLM Model ) - YouTube, accessed July 31, 2025, [https://www.youtube.com/watch?v=FMiVxQ7QwRU&pp=0gcJCfwAo7VqN5tD](https://www.youtube.com/watch?v=FMiVxQ7QwRU&pp=0gcJCfwAo7VqN5tD)  
30. agentchat.conversable_agent | AutoGen 0.2, accessed July 31, 2025, [https://microsoft.github.io/autogen/0.2/docs/reference/agentchat/conversable_agent/](https://microsoft.github.io/autogen/0.2/docs/reference/agentchat/conversable_agent/)  
31. autogen_ext.models.openai — AutoGen - Microsoft Open Source, accessed July 31, 2025, [https://microsoft.github.io/autogen/stable//reference/python/autogen_ext.models.openai.html](https://microsoft.github.io/autogen/stable//reference/python/autogen_ext.models.openai.html)  
32. LLM Configuration | AutoGen 0.2 - Microsoft Open Source, accessed July 31, 2025, [https://microsoft.github.io/autogen/0.2/docs/topics/llm_configuration/](https://microsoft.github.io/autogen/0.2/docs/topics/llm_configuration/)  
33. Build AutoGen Agents with Qwen3: Structured Output & Thinking Mode - Data Leads Future, accessed July 31, 2025, [https://www.dataleadsfuture.com/build-autogen-agents-with-qwen3-structured-output-thinking-mode/](https://www.dataleadsfuture.com/build-autogen-agents-with-qwen3-structured-output-thinking-mode/)  
34. Logging — AutoGen - Microsoft Open Source, accessed July 31, 2025, [https://microsoft.github.io/autogen/stable//user-guide/agentchat-user-guide/logging.html](https://microsoft.github.io/autogen/stable//user-guide/agentchat-user-guide/logging.html)  
35. Logging — AutoGen - Microsoft Open Source, accessed July 31, 2025, [https://microsoft.github.io/autogen/stable//user-guide/core-user-guide/framework/logging.html](https://microsoft.github.io/autogen/stable//user-guide/core-user-guide/framework/logging.html)  
36. Tracking LLM usage with a logger — AutoGen - Microsoft Open Source, accessed July 31, 2025, [https://microsoft.github.io/autogen/stable//user-guide/core-user-guide/cookbook/llm-usage-logger.html](https://microsoft.github.io/autogen/stable//user-guide/core-user-guide/cookbook/llm-usage-logger.html)  
37. Runtime Logging with AutoGen - Microsoft Open Source, accessed July 31, 2025, [https://microsoft.github.io/autogen/0.2/docs/notebooks/agentchat_logging/](https://microsoft.github.io/autogen/0.2/docs/notebooks/agentchat_logging/)  
38. AI Agents XIII : Autogen :“The multi agent conversation Framework ” — 1 - Medium, accessed July 31, 2025, [https://medium.com/@danushidk507/ai-agents-xiii-autogen-the-multi-agent-conversation-framework-1-fbda3e34b47e](https://medium.com/@danushidk507/ai-agents-xiii-autogen-the-multi-agent-conversation-framework-1-fbda3e34b47e)  
39. How AutoGen Simplifies Complex AI Workflows with Multi-Agent Conversations - Medium, accessed July 31, 2025, [https://medium.com/@tahirbalarabe2/how-autogen-simplifies-complex-ai-workflows-with-multi-agent-conversations-8c77928cd77f](https://medium.com/@tahirbalarabe2/how-autogen-simplifies-complex-ai-workflows-with-multi-agent-conversations-8c77928cd77f)  
40. Tests | AutoGen 0.2 - Microsoft Open Source, accessed July 31, 2025, [https://microsoft.github.io/autogen/0.2/docs/contributor-guide/tests/](https://microsoft.github.io/autogen/0.2/docs/contributor-guide/tests/)  
41. AutoGenBench -- A Tool for Measuring and Evaluating AutoGen Agents, accessed July 31, 2025, [https://microsoft.github.io/autogen/0.2/blog/2024/01/25/AutoGenBench/](https://microsoft.github.io/autogen/0.2/blog/2024/01/25/AutoGenBench/)  
42. AutoGen, accessed July 31, 2025, [https://microsoft.github.io/autogen/stable//index.html](https://microsoft.github.io/autogen/stable//index.html)
