"""
Mock diagnostic tool servers for testing and demonstration.
These simulate the external K8s, SLS, and ARMS diagnostic services.
"""

import json
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
import uvicorn
import threading

class DiagnosticRequest(BaseModel):
    """Request model for diagnostic API."""
    problem: str
    namespace: str = "default"
    domain: str
    additional_params: Dict[str, Any] = {}

class DiagnosticResponse(BaseModel):
    """Response model for diagnostic API."""
    domain: str
    status: str
    findings: List[str]
    raw_data: Dict[str, Any]
    timestamp: str
    execution_time: float

class MockK8sDiagnosticServer:
    """Mock Kubernetes diagnostic server."""
    
    def diagnose(self, request: DiagnosticRequest) -> DiagnosticResponse:
        """Simulate K8s diagnostic analysis."""
        start_time = time.time()
        
        # Simulate some processing time
        time.sleep(0.5)
        
        # Mock findings based on problem description
        findings = []
        raw_data = {}
        
        if "oom" in request.problem.lower() or "memory" in request.problem.lower():
            findings = [
                f"Found 3 pods in {request.namespace} namespace with OOMKilled status",
                "Memory limits set to 2Gi, but usage peaks at 2.8Gi",
                "Node memory pressure detected on 2 out of 5 nodes",
                "Recent events show 15 OOMKilled events in last 24 hours"
            ]
            
            raw_data = {
                "pods": [
                    {
                        "name": "checkout-service-7d4b8c9f-abc12",
                        "namespace": request.namespace,
                        "status": "OOMKilled",
                        "restartCount": 8,
                        "memoryLimit": "2Gi",
                        "memoryUsage": "2.8Gi"
                    },
                    {
                        "name": "checkout-service-7d4b8c9f-def34",
                        "namespace": request.namespace,
                        "status": "Running",
                        "restartCount": 12,
                        "memoryLimit": "2Gi",
                        "memoryUsage": "2.7Gi"
                    }
                ],
                "nodes": [
                    {
                        "name": "node-1",
                        "memoryPressure": True,
                        "availableMemory": "1.2Gi",
                        "totalMemory": "16Gi"
                    },
                    {
                        "name": "node-2", 
                        "memoryPressure": True,
                        "availableMemory": "0.8Gi",
                        "totalMemory": "16Gi"
                    }
                ],
                "events": [
                    {
                        "type": "Warning",
                        "reason": "OOMKilling",
                        "message": "Memory cgroup out of memory: Killed process 1234",
                        "timestamp": (datetime.now() - timedelta(minutes=30)).isoformat()
                    }
                ]
            }
        else:
            findings = ["No obvious K8s issues detected", "All pods running normally"]
            raw_data = {"pods": [], "events": [], "nodes": []}
        
        execution_time = time.time() - start_time
        
        return DiagnosticResponse(
            domain="k8s",
            status="success",
            findings=findings,
            raw_data=raw_data,
            timestamp=datetime.now().isoformat(),
            execution_time=execution_time
        )

class MockSLSDiagnosticServer:
    """Mock SLS (Simple Log Service) diagnostic server."""
    
    def diagnose(self, request: DiagnosticRequest) -> DiagnosticResponse:
        """Simulate SLS log analysis."""
        start_time = time.time()
        
        # Simulate processing time
        time.sleep(0.3)
        
        findings = []
        raw_data = {}
        
        if "oom" in request.problem.lower() or "memory" in request.problem.lower():
            findings = [
                "Found 45 OOM-related log entries in last 24 hours",
                "Memory allocation failures detected in application logs",
                "Java heap space errors correlating with OOM events",
                "Log pattern shows memory usage spikes during peak traffic"
            ]
            
            raw_data = {
                "log_entries": [
                    {
                        "timestamp": (datetime.now() - timedelta(hours=2)).isoformat(),
                        "level": "ERROR",
                        "message": "java.lang.OutOfMemoryError: Java heap space",
                        "source": "checkout-service",
                        "namespace": request.namespace
                    },
                    {
                        "timestamp": (datetime.now() - timedelta(hours=1)).isoformat(),
                        "level": "WARN",
                        "message": "Memory usage at 95% of allocated heap",
                        "source": "checkout-service",
                        "namespace": request.namespace
                    }
                ],
                "aggregations": {
                    "oom_events_24h": 45,
                    "error_rate": "12.5%",
                    "peak_memory_time": "14:30-15:30 UTC"
                }
            }
        else:
            findings = ["No memory-related errors in logs", "Application logs appear normal"]
            raw_data = {"log_entries": [], "aggregations": {}}
        
        execution_time = time.time() - start_time
        
        return DiagnosticResponse(
            domain="sls",
            status="success", 
            findings=findings,
            raw_data=raw_data,
            timestamp=datetime.now().isoformat(),
            execution_time=execution_time
        )

class MockARMSDiagnosticServer:
    """Mock ARMS (Application Real-time Monitoring Service) diagnostic server."""
    
    def diagnose(self, request: DiagnosticRequest) -> DiagnosticResponse:
        """Simulate ARMS metrics analysis."""
        start_time = time.time()
        
        # Simulate processing time
        time.sleep(0.4)
        
        findings = []
        raw_data = {}
        
        if "oom" in request.problem.lower() or "memory" in request.problem.lower():
            findings = [
                "Memory usage trending upward over last 7 days",
                "Container memory RSS consistently above 2.5Gi",
                "Memory allocation rate exceeding deallocation by 15%",
                "GC pressure indicators showing memory stress"
            ]
            
            raw_data = {
                "metrics": {
                    "container_memory_usage_bytes": {
                        "current": 2.8 * 1024**3,  # 2.8Gi in bytes
                        "peak_24h": 3.1 * 1024**3,
                        "average_24h": 2.6 * 1024**3
                    },
                    "container_memory_rss": {
                        "current": 2.7 * 1024**3,
                        "trend": "increasing"
                    },
                    "node_memory_utilization": {
                        "node-1": 0.85,
                        "node-2": 0.92,
                        "node-3": 0.78
                    }
                },
                "alerts": [
                    {
                        "name": "HighMemoryUsage",
                        "status": "firing",
                        "threshold": "80%",
                        "current": "92%"
                    }
                ]
            }
        else:
            findings = ["Memory metrics within normal ranges", "No performance anomalies detected"]
            raw_data = {"metrics": {}, "alerts": []}
        
        execution_time = time.time() - start_time
        
        return DiagnosticResponse(
            domain="arms",
            status="success",
            findings=findings,
            raw_data=raw_data,
            timestamp=datetime.now().isoformat(),
            execution_time=execution_time
        )

def create_mock_server_app() -> FastAPI:
    """Create FastAPI app with all mock diagnostic endpoints."""
    app = FastAPI(title="Mock Diagnostic Tool Server", version="1.0.0")
    
    # Initialize mock servers
    k8s_server = MockK8sDiagnosticServer()
    sls_server = MockSLSDiagnosticServer()
    arms_server = MockARMSDiagnosticServer()
    
    @app.post("/k8s/diagnose", response_model=DiagnosticResponse)
    async def k8s_diagnose(request: DiagnosticRequest):
        """K8s diagnostic endpoint."""
        try:
            request.domain = "k8s"
            return k8s_server.diagnose(request)
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    
    @app.post("/sls/diagnose", response_model=DiagnosticResponse)
    async def sls_diagnose(request: DiagnosticRequest):
        """SLS diagnostic endpoint."""
        try:
            request.domain = "sls"
            return sls_server.diagnose(request)
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    
    @app.post("/arms/diagnose", response_model=DiagnosticResponse)
    async def arms_diagnose(request: DiagnosticRequest):
        """ARMS diagnostic endpoint."""
        try:
            request.domain = "arms"
            return arms_server.diagnose(request)
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    
    @app.get("/health")
    async def health_check():
        """Health check endpoint."""
        return {"status": "healthy", "timestamp": datetime.now().isoformat()}
    
    return app

def run_mock_server(port: int = 8080):
    """Run the mock diagnostic server."""
    app = create_mock_server_app()
    uvicorn.run(app, host="0.0.0.0", port=port)

def start_mock_server_background(port: int = 8080):
    """Start mock server in background thread."""
    server_thread = threading.Thread(
        target=run_mock_server,
        args=(port,),
        daemon=True
    )
    server_thread.start()
    return server_thread

if __name__ == "__main__":
    print("Starting mock diagnostic tool server on port 8080...")
    run_mock_server(8080)
