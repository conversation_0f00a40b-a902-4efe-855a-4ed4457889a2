"""
Validator Agent for OOM problem resolution verification.
Responsible for verifying the effectiveness of remediation actions and generating final reports.
"""

import autogen
from pydantic import BaseModel
from ..core.config import get_validator_llm_config
from ..core.models import FinalReport

VALIDATOR_SYSTEM_MESSAGE = """
You are a senior Site Reliability Engineer (SRE) responsible for final validation and reporting of OOM problem resolution.

Your role is to synthesize all diagnostic information, execution results, and verification data to determine if the OOM problem has been successfully resolved.

## Your Responsibilities:

1. **Analyze All Evidence**: 
   - Review initial problem description
   - Examine diagnostic reports from K8s, SLS, and ARMS teams
   - Evaluate execution results and their impact
   - Consider verification data from post-execution checks

2. **Determine Resolution Status**:
   - Assess whether the OOM problem has been resolved
   - Calculate confidence level in the resolution
   - Identify any remaining risks or issues

3. **Generate Comprehensive Report**:
   - Use the `generate_final_report` tool to create a structured report
   - Include root cause analysis
   - Document all operations performed
   - Provide current system status
   - Offer long-term recommendations

## Evaluation Criteria:

**Problem Resolved (is_resolved=True) when**:
- No more OOMKilled events occurring
- Memory usage is within acceptable limits
- Pods are running stably
- Node memory pressure is relieved
- Monitoring shows healthy metrics

**Problem Not Resolved (is_resolved=False) when**:
- OOMKilled events continue to occur
- Memory usage still exceeds limits
- Pods continue to crash or restart
- Node memory pressure persists
- Monitoring shows concerning trends

## Confidence Scoring:
- 0.9-1.0: High confidence - Strong evidence of resolution with comprehensive verification
- 0.7-0.8: Good confidence - Clear improvement with some verification gaps
- 0.5-0.6: Moderate confidence - Some improvement but concerns remain
- 0.3-0.4: Low confidence - Minimal improvement or mixed results
- 0.0-0.2: Very low confidence - No improvement or worsening conditions

## Report Requirements:
- Always use the `generate_final_report` tool
- Provide clear, actionable insights
- Base conclusions on evidence, not assumptions
- Include specific recommendations for prevention
- Maintain professional, technical language

Your analysis must be thorough, evidence-based, and actionable for the operations team.
"""

def generate_final_report(
    is_resolved: bool,
    confidence_score: float,
    summary: str,
    root_cause: str,
    operations_performed: list,
    current_status: str,
    long_term_recommendations: list,
    reasoning: str
) -> str:
    """
    Generate a structured final report for OOM problem resolution.
    
    Args:
        is_resolved: Whether the OOM problem was successfully resolved
        confidence_score: Confidence level (0.0 to 1.0)
        summary: Concise summary of the problem and resolution
        root_cause: Identified root cause of the OOM issue
        operations_performed: List of operations that were executed
        current_status: Current health status of the system
        long_term_recommendations: Recommendations to prevent future issues
        reasoning: Detailed reasoning for the conclusions
        
    Returns:
        JSON string with the final report
    """
    try:
        report = FinalReport(
            is_resolved=is_resolved,
            confidence_score=confidence_score,
            summary=summary,
            root_cause=root_cause,
            operations_performed=operations_performed,
            current_status=current_status,
            long_term_recommendations=long_term_recommendations,
            reasoning=reasoning
        )
        
        return report.model_dump_json(indent=2)
        
    except Exception as e:
        error_report = {
            "error": f"Failed to generate final report: {str(e)}",
            "is_resolved": False,
            "confidence_score": 0.0,
            "summary": "Report generation failed"
        }
        return str(error_report)

def get_validator_agent() -> autogen.AssistantAgent:
    """
    Initialize and return the Validator agent.
    
    Returns:
        autogen.AssistantAgent: Configured validator agent with structured output
    """
    # Get enhanced LLM config for structured output
    validator_llm_config = get_validator_llm_config()
    
    validator = autogen.AssistantAgent(
        name="Validator",
        llm_config=validator_llm_config,
        system_message=VALIDATOR_SYSTEM_MESSAGE,
        description="Senior SRE responsible for validating OOM problem resolution and generating final reports."
    )
    
    # Register the final report generation tool
    validator.register_for_llm(
        name="generate_final_report",
        description="Generate a structured final report for OOM problem resolution."
    )(generate_final_report)
    
    return validator
