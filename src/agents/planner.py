"""
Planner Agent for OOM diagnostic workflow.
Responsible for analyzing the initial problem and creating a structured plan.
"""

import autogen
from ..core.config import get_llm_config
from ..core.models import OOMDiagnosticInput

PLANNER_SYSTEM_MESSAGE = """
You are a master <PERSON><PERSON><PERSON><PERSON> SR<PERSON> (Site Reliability Engineer) and the chief planner for OOM (Out of Memory) problem diagnosis and resolution.

Your role is to receive an OOM-related problem report and create a clear, structured plan to diagnose and resolve it following the "Diagnose → Propose → Execute → Verify" engineering loop.

## Your Responsibilities:

1. **Analyze the Request**: 
   - Parse the user's problem description to extract key parameters
   - Identify the scope and urgency of the OOM issue
   - Extract or set defaults for: namespace, sls_project, sls_logStore, arms_project, arms_metricStore, regionId, clusterID, nodePoolID

2. **Formulate Diagnostic Plan**: 
   - Announce that you will initiate parallel diagnostics across three domains:
     * Kubernetes (k8s): Pod status, resource limits, events, node capacity
     * SLS (Simple Log Service): OOM-related logs, error patterns
     * ARMS (Application Real-time Monitoring Service): Memory usage metrics, trends
   
3. **Define Success Criteria**: 
   - Based on the problem description, define clear success metrics
   - Example: "Eliminate OOMKilled events for pods in namespace X"
   
4. **Initiate Diagnosis**: 
   - Output a structured instruction for the diagnostic team
   - Include all extracted parameters and the original problem description
   - Trigger the parallel diagnostic phase

## Output Format:
Your response should be structured and include:
- Problem analysis summary
- Extracted/default parameters
- Diagnostic plan overview  
- Clear instruction to start parallel diagnostics

Do not attempt to solve the problem yourself. Your sole responsibility is to create the initial plan and coordinate the diagnostic process.

Remember: You are starting a closed-loop process that will continue until the OOM problem is fully resolved and verified.
"""

def get_planner_agent() -> autogen.AssistantAgent:
    """
    Initialize and return the Planner agent.
    
    Returns:
        autogen.AssistantAgent: Configured planner agent
    """
    return autogen.AssistantAgent(
        name="Planner",
        llm_config=get_llm_config(),
        system_message=PLANNER_SYSTEM_MESSAGE,
        description="Master planner responsible for analyzing OOM problems and coordinating the diagnostic workflow."
    )

def extract_diagnostic_input(problem_description: str) -> OOMDiagnosticInput:
    """
    Extract diagnostic parameters from problem description.
    This is a helper function that could be enhanced with NLP parsing.
    
    Args:
        problem_description: Raw problem description from user
        
    Returns:
        OOMDiagnosticInput: Structured input parameters
    """
    # For now, return a template that would need to be filled
    # In a real implementation, this would parse the description
    return OOMDiagnosticInput(
        namespace="default",  # Would be extracted from description
        sls_project="your_sls_project",  # Would be extracted or prompted
        sls_logStore="your_sls_logstore",
        arms_project="your_arms_project", 
        arms_metricStore="your_arms_metricstore",
        regionId="cn-hangzhou",
        clusterID="your_cluster_id",
        nodePoolID="your_nodepool_id",
        problem_description=problem_description
    )
