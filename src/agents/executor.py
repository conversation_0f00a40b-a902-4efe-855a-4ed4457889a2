"""
Executor Agent for OOM problem resolution.
Responsible for executing the remediation plan generated by the diagnostic process.
"""

import autogen
import requests
import json
import time
from typing import Annotated, Dict, Any, List, Optional
from ..core.config import get_cluster_config, get_llm_config, get_llm_config

EXECUTOR_SYSTEM_MESSAGE = """
You are an automated execution engine for Kubernetes OOM problem resolution.

Your role is to execute the remediation plan provided by the diagnostic team. You have access to tools for:
1. Kubernetes resource management (kubectl operations)
2. Node pool scaling operations
3. Configuration updates

## Your Responsibilities:

1. **Receive Execution Plan**: Accept a structured execution plan from the diagnostic team
2. **Execute Operations**: Perform the specified operations in the correct order
3. **Monitor Progress**: Track the execution of each operation
4. **Report Results**: Provide detailed feedback on execution results

## Available Operations:
- `patch_deployment_resources`: Adjust memory limits and requests for deployments
- `scale_nodepool`: Scale node pools to add more capacity
- `execute_kubectl_command`: Execute custom kubectl commands
- `verify_operation`: Verify that an operation completed successfully

## Execution Principles:
- Execute operations in the specified order
- Verify each operation before proceeding to the next
- Provide detailed logging of all actions
- Report both successes and failures clearly
- Do not modify the plan - execute exactly as specified

## Safety Measures:
- All operations are executed in a controlled environment
- Each operation includes verification steps
- Rollback procedures are available if needed

When you receive an execution plan, confirm the operations and execute them systematically.
"""


class KubernetesExecutor:
    """Kubernetes operations executor."""

    def __init__(self):
        self.cluster_config = get_cluster_config()

    def patch_deployment_resources(
        self,
        deployment_name: Annotated[str, "Name of the deployment to patch"],
        namespace: Annotated[str, "Kubernetes namespace"] = "default",
        memory_limit: Annotated[Optional[str],
                                "New memory limit (e.g., '2Gi')"] = None,
        memory_request: Annotated[Optional[str],
                                  "New memory request (e.g., '1Gi')"] = None,
        cpu_limit: Annotated[Optional[str],
                             "New CPU limit (e.g., '1000m')"] = None,
        cpu_request: Annotated[Optional[str],
                               "New CPU request (e.g., '500m')"] = None
    ) -> str:
        """
        Patch deployment resource limits and requests.

        Args:
            deployment_name: Name of the deployment
            namespace: Kubernetes namespace
            memory_limit: New memory limit
            memory_request: New memory request
            cpu_limit: New CPU limit
            cpu_request: New CPU request

        Returns:
            JSON string with operation result
        """
        start_time = time.time()

        try:
            # Build the patch payload
            patch_data = {
                "spec": {
                    "template": {
                        "spec": {
                            "containers": [{
                                "name": deployment_name,  # Assuming container name matches deployment
                                "resources": {}
                            }]
                        }
                    }
                }
            }

            resources = {}
            if memory_limit or cpu_limit:
                limits = {}
                if memory_limit:
                    limits["memory"] = memory_limit
                if cpu_limit:
                    limits["cpu"] = cpu_limit
                resources["limits"] = limits

            if memory_request or cpu_request:
                requests = {}
                if memory_request:
                    requests["memory"] = memory_request
                if cpu_request:
                    requests["cpu"] = cpu_request
                resources["requests"] = requests

            patch_data["spec"]["template"]["spec"]["containers"][0]["resources"] = resources

            # In a real implementation, this would use kubectl or Kubernetes Python client
            # For now, simulate the operation
            execution_time = time.time() - start_time

            result = {
                "operation": "patch_deployment_resources",
                "success": True,
                "deployment": deployment_name,
                "namespace": namespace,
                "changes": resources,
                "execution_time": execution_time,
                "message": f"Successfully patched deployment {deployment_name} in namespace {namespace}"
            }

            return json.dumps(result)

        except Exception as e:
            execution_time = time.time() - start_time

            error_result = {
                "operation": "patch_deployment_resources",
                "success": False,
                "deployment": deployment_name,
                "namespace": namespace,
                "error": str(e),
                "execution_time": execution_time
            }

            return json.dumps(error_result)

    def scale_nodepool(
        self,
        target_size: Annotated[int, "Target number of nodes in the pool"],
        cluster_id: Annotated[Optional[str], "Cluster ID"] = None,
        nodepool_id: Annotated[Optional[str], "Node pool ID"] = None
    ) -> str:
        """
        Scale a node pool to the specified size.

        Args:
            target_size: Target number of nodes
            cluster_id: Cluster ID (uses default if not provided)
            nodepool_id: Node pool ID (uses default if not provided)

        Returns:
            JSON string with operation result
        """
        start_time = time.time()

        try:
            cluster_id = cluster_id or self.cluster_config["cluster_id"]
            nodepool_id = nodepool_id or self.cluster_config["node_pool_id"]

            if not cluster_id or not nodepool_id:
                raise ValueError(
                    "Cluster ID and Node Pool ID must be provided")

            # In a real implementation, this would call the cloud provider's API
            # For now, simulate the operation
            execution_time = time.time() - start_time

            result = {
                "operation": "scale_nodepool",
                "success": True,
                "cluster_id": cluster_id,
                "nodepool_id": nodepool_id,
                "target_size": target_size,
                "execution_time": execution_time,
                "message": f"Successfully initiated scaling of node pool {nodepool_id} to {target_size} nodes"
            }

            return json.dumps(result)

        except Exception as e:
            execution_time = time.time() - start_time

            error_result = {
                "operation": "scale_nodepool",
                "success": False,
                "cluster_id": cluster_id,
                "nodepool_id": nodepool_id,
                "target_size": target_size,
                "error": str(e),
                "execution_time": execution_time
            }

            return json.dumps(error_result)

    def execute_kubectl_command(
        self,
        command: Annotated[str, "kubectl command to execute"],
        namespace: Annotated[str, "Kubernetes namespace"] = "default"
    ) -> str:
        """
        Execute a custom kubectl command.

        Args:
            command: kubectl command to execute
            namespace: Kubernetes namespace

        Returns:
            JSON string with command output
        """
        start_time = time.time()

        try:
            # In a real implementation, this would execute the actual kubectl command
            # For now, simulate the operation
            execution_time = time.time() - start_time

            result = {
                "operation": "execute_kubectl_command",
                "success": True,
                "command": command,
                "namespace": namespace,
                "output": f"Simulated output for: {command}",
                "execution_time": execution_time
            }

            return json.dumps(result)

        except Exception as e:
            execution_time = time.time() - start_time

            error_result = {
                "operation": "execute_kubectl_command",
                "success": False,
                "command": command,
                "namespace": namespace,
                "error": str(e),
                "execution_time": execution_time
            }

            return json.dumps(error_result)


def get_executor_agent() -> autogen.UserProxyAgent:
    """
    Initialize and return the Executor agent.

    Returns:
        autogen.UserProxyAgent: Configured executor agent
    """
    k8s_executor = KubernetesExecutor()

    executor = autogen.UserProxyAgent(
        name="Executor",
        human_input_mode="NEVER",
        system_message=EXECUTOR_SYSTEM_MESSAGE,
        llm_config=get_llm_config(),  # Required for tool registration
        code_execution_config={
            "work_dir": "execution_context",
            "use_docker": False,  # Set to True in production for better isolation
        },
        description="Automated executor for Kubernetes OOM remediation operations."
    )

    # Create wrapper functions for tool registration
    def patch_deployment_resources(namespace: str, deployment_name: str,
                                   memory_limit: str, memory_request: str,
                                   cpu_limit: str = None, cpu_request: str = None) -> str:
        return k8s_executor.patch_deployment_resources(
            namespace, deployment_name, memory_limit, memory_request, cpu_limit, cpu_request
        )

    def scale_nodepool(cluster_id: str, nodepool_id: str, desired_size: int, region_id: str) -> str:
        return k8s_executor.scale_nodepool(cluster_id, nodepool_id, desired_size, region_id)

    def execute_kubectl_command(command: str, namespace: str = None) -> str:
        return k8s_executor.execute_kubectl_command(command, namespace)

    # Register Kubernetes operation tools
    executor.register_for_llm(
        name="patch_deployment_resources",
        description="Patch deployment resource limits and requests."
    )(patch_deployment_resources)

    executor.register_for_llm(
        name="scale_nodepool",
        description="Scale a node pool to add more capacity."
    )(scale_nodepool)

    executor.register_for_llm(
        name="execute_kubectl_command",
        description="Execute a custom kubectl command."
    )(execute_kubectl_command)

    return executor
