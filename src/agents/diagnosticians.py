"""
Diagnostic Agents for K8s, SLS, and ARMS domains using MCP.
Each agent is a specialist in their respective domain.
"""

import autogen
from autogen_agentchat.agents import AssistantAgent as AgentChatAssistantAgent
from autogen_ext.tools.mcp import McpWorkbench
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_core.models import ModelInfo
from ..core.config import get_llm_config, get_mcp_server_params

# Base system message template for diagnostic agents
DIAGNOSTICIAN_SYSTEM_MESSAGE_TEMPLATE = """
You are a specialized {domain} diagnostic expert for Kubernetes OOM (Out of Memory) problem analysis.

Your expertise focuses on {domain_description}.

## Your Responsibilities:

1. **Use Diagnostic Tools**: Call the `diagnose_problem` function with the provided problem description and parameters
2. **Analyze Results**: Process the raw data returned by your diagnostic tools
3. **Generate Structured Report**: Create a comprehensive DiagnosticReport in JSON format

## Diagnostic Focus for {domain}:
{diagnostic_focus}

## Output Requirements:
- Always call the `diagnose_problem` tool first
- Analyze the returned data thoroughly
- Generate a structured JSON report with:
  * domain: "{domain}"
  * findings: List of key discoveries
  * raw_data: Complete tool output
  * status: "success", "error", or "partial"
  * timestamp: Current time

## Important Notes:
- Do not interpret findings beyond your domain expertise
- Do not suggest solutions - only report diagnostic data
- If tool calls fail, report the error and attempt alternative approaches
- Focus on OOM-related indicators in your domain
"""


# MCP Workbench will be used instead of direct tool calls


async def get_k8s_diagnostician_with_workbench() -> tuple[AgentChatAssistantAgent, McpWorkbench]:
    """Initialize and return the K8s Diagnostician agent with MCP workbench."""

    diagnostic_focus = """
    - Pod status analysis (OOMKilled, CrashLoopBackOff states)
    - Resource requests and limits examination
    - Node capacity and memory pressure
    - Kubernetes events related to memory issues
    - Pod scheduling and resource allocation
    """

    system_message = DIAGNOSTICIAN_SYSTEM_MESSAGE_TEMPLATE.format(
        domain="Kubernetes (k8s)",
        domain_description="Kubernetes cluster state, pod lifecycle, and resource management",
        diagnostic_focus=diagnostic_focus
    )

    # Create MCP workbench
    mcp_params = get_mcp_server_params()
    workbench = McpWorkbench(server_params=mcp_params)

    # Create model client for AgentChat
    model_info: ModelInfo = {
        "family": "openai",
        "vision": False,
        "function_calling": True,
        "json_output": True,
        "structured_output": False
    }

    model_client = OpenAIChatCompletionClient(
        model="qwen-plus",
        base_url=get_llm_config()["config_list"][0]["base_url"],
        api_key=get_llm_config()["config_list"][0]["api_key"],
        model_info=model_info
    )

    # Create agent with workbench
    agent = AgentChatAssistantAgent(
        name="K8s_Diagnostician",
        model_client=model_client,
        workbench=workbench,
        system_message=system_message,
        description="Kubernetes expert specializing in OOM-related pod and cluster analysis."
    )

    return agent, workbench


def get_k8s_diagnostician() -> autogen.AssistantAgent:
    """Initialize and return the K8s Diagnostician agent (legacy function)."""

    diagnostic_focus = """
    - Pod status analysis (OOMKilled, CrashLoopBackOff states)
    - Resource requests and limits examination
    - Node capacity and memory pressure
    - Kubernetes events related to memory issues
    - Pod scheduling and resource allocation
    """

    system_message = DIAGNOSTICIAN_SYSTEM_MESSAGE_TEMPLATE.format(
        domain="Kubernetes (k8s)",
        domain_description="Kubernetes cluster state, pod lifecycle, and resource management",
        diagnostic_focus=diagnostic_focus
    )

    agent = autogen.AssistantAgent(
        name="K8s_Diagnostician",
        llm_config=get_llm_config(),
        system_message=system_message,
        description="Kubernetes expert specializing in OOM-related pod and cluster analysis."
    )

    return agent


async def get_sls_diagnostician_with_workbench() -> tuple[AgentChatAssistantAgent, McpWorkbench]:
    """Initialize and return the SLS Diagnostician agent with MCP workbench."""

    diagnostic_focus = """
    - Log analysis for OOM-related events
    - Error pattern detection in application logs
    - System log examination for memory pressure indicators
    - Log aggregation and trend analysis
    - Correlation of log events with OOM occurrences
    """

    system_message = DIAGNOSTICIAN_SYSTEM_MESSAGE_TEMPLATE.format(
        domain="SLS (Simple Log Service)",
        domain_description="log aggregation, analysis, and pattern detection",
        diagnostic_focus=diagnostic_focus
    )

    # Create MCP workbench
    mcp_params = get_mcp_server_params()
    workbench = McpWorkbench(server_params=mcp_params)

    # Create model client for AgentChat
    model_info: ModelInfo = {
        "family": "openai",
        "vision": False,
        "function_calling": True,
        "json_output": True,
        "structured_output": False
    }

    model_client = OpenAIChatCompletionClient(
        model="qwen-plus",
        base_url=get_llm_config()["config_list"][0]["base_url"],
        api_key=get_llm_config()["config_list"][0]["api_key"],
        model_info=model_info
    )

    # Create agent with workbench
    agent = AgentChatAssistantAgent(
        name="SLS_Diagnostician",
        model_client=model_client,
        workbench=workbench,
        system_message=system_message,
        description="SLS expert specializing in log analysis for OOM problem investigation."
    )

    return agent, workbench


def get_sls_diagnostician() -> autogen.AssistantAgent:
    """Initialize and return the SLS Diagnostician agent (legacy function)."""

    diagnostic_focus = """
    - Log analysis for OOM-related events
    - Error pattern detection in application logs
    - System log examination for memory pressure indicators
    - Log aggregation and trend analysis
    - Correlation of log events with OOM occurrences
    """

    system_message = DIAGNOSTICIAN_SYSTEM_MESSAGE_TEMPLATE.format(
        domain="SLS (Simple Log Service)",
        domain_description="log aggregation, analysis, and pattern detection",
        diagnostic_focus=diagnostic_focus
    )

    agent = autogen.AssistantAgent(
        name="SLS_Diagnostician",
        llm_config=get_llm_config(),
        system_message=system_message,
        description="SLS expert specializing in log analysis for OOM problem investigation."
    )

    return agent


async def get_arms_diagnostician_with_workbench() -> tuple[AgentChatAssistantAgent, McpWorkbench]:
    """Initialize and return the ARMS Diagnostician agent with MCP workbench."""

    diagnostic_focus = """
    - Memory usage metrics and trends analysis
    - Container memory consumption patterns
    - Node-level memory pressure monitoring
    - Performance metrics correlation with OOM events
    - Historical memory usage trend analysis
    """

    system_message = DIAGNOSTICIAN_SYSTEM_MESSAGE_TEMPLATE.format(
        domain="ARMS (Application Real-time Monitoring Service)",
        domain_description="application performance monitoring and metrics analysis",
        diagnostic_focus=diagnostic_focus
    )

    # Create MCP workbench
    mcp_params = get_mcp_server_params()
    workbench = McpWorkbench(server_params=mcp_params)

    # Create model client for AgentChat
    model_info: ModelInfo = {
        "family": "openai",
        "vision": False,
        "function_calling": True,
        "json_output": True,
        "structured_output": False
    }

    model_client = OpenAIChatCompletionClient(
        model="qwen-plus",
        base_url=get_llm_config()["config_list"][0]["base_url"],
        api_key=get_llm_config()["config_list"][0]["api_key"],
        model_info=model_info
    )

    # Create agent with workbench
    agent = AgentChatAssistantAgent(
        name="ARMS_Diagnostician",
        model_client=model_client,
        workbench=workbench,
        system_message=system_message,
        description="ARMS expert specializing in memory metrics and performance monitoring."
    )

    return agent, workbench


def get_arms_diagnostician() -> autogen.AssistantAgent:
    """Initialize and return the ARMS Diagnostician agent (legacy function)."""

    diagnostic_focus = """
    - Memory usage metrics and trends analysis
    - Container memory consumption patterns
    - Node-level memory pressure monitoring
    - Performance metrics correlation with OOM events
    - Historical memory usage trend analysis
    """

    system_message = DIAGNOSTICIAN_SYSTEM_MESSAGE_TEMPLATE.format(
        domain="ARMS (Application Real-time Monitoring Service)",
        domain_description="application performance monitoring and metrics analysis",
        diagnostic_focus=diagnostic_focus
    )

    agent = autogen.AssistantAgent(
        name="ARMS_Diagnostician",
        llm_config=get_llm_config(),
        system_message=system_message,
        description="ARMS expert specializing in memory metrics and performance monitoring."
    )

    return agent
