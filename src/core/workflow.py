"""
Workflow management for the OOM diagnostic system.
Implements state-driven group chat control and agent coordination with MCP support.
"""

import autogen
import asyncio
from typing import List, Dict, Any, Optional
from autogen_ext.tools.mcp import McpWorkbench
from ..core.config import get_llm_config
from ..core.models import AgentState
from ..agents.planner import get_planner_agent
from ..agents.diagnosticians import (
    get_k8s_diagnostician, get_sls_diagnostician, get_arms_diagnostician,
    get_k8s_diagnostician_with_workbench, get_sls_diagnostician_with_workbench,
    get_arms_diagnostician_with_workbench
)
from ..agents.executor import get_executor_agent
from ..agents.validator import get_validator_agent


class OOMDiagnosticWorkflowMCP:
    """
    Manages the OOM diagnostic workflow using state-driven group chat with MCP support.
    Implements the "Diagnose → Propose → Execute → Verify" loop.
    """

    def __init__(self):
        self.state = AgentState()
        self.agents = {}
        self.workbenches = {}
        self.diagnostic_agents = []
        self.diagnostic_reports = {}
        self._initialized = False

    async def initialize(self):
        """Initialize agents with MCP workbenches asynchronously."""
        if self._initialized:
            return

        # Initialize diagnostic agents with MCP workbenches
        k8s_agent, k8s_workbench = await get_k8s_diagnostician_with_workbench()
        sls_agent, sls_workbench = await get_sls_diagnostician_with_workbench()
        arms_agent, arms_workbench = await get_arms_diagnostician_with_workbench()

        self.agents = {
            "planner": get_planner_agent(),
            "k8s_diagnostician": k8s_agent,
            "sls_diagnostician": sls_agent,
            "arms_diagnostician": arms_agent,
            "executor": get_executor_agent(),
            "validator": get_validator_agent()
        }

        self.workbenches = {
            "k8s": k8s_workbench,
            "sls": sls_workbench,
            "arms": arms_workbench
        }

        self.diagnostic_agents = [
            self.agents["k8s_diagnostician"],
            self.agents["sls_diagnostician"],
            self.agents["arms_diagnostician"]
        ]

        self._initialized = True

    def custom_speaker_selection(
        self,
        last_speaker: autogen.Agent,
        groupchat: autogen.GroupChat
    ) -> autogen.Agent:
        """
        Custom speaker selection function implementing state-driven workflow.

        Args:
            last_speaker: The agent who spoke last
            groupchat: The group chat instance

        Returns:
            Next agent to speak
        """
        messages = groupchat.messages

        # Initial state: Start with planner
        if not messages or last_speaker is None:
            self.state.current_phase = "planning"
            return self.agents["planner"]

        # Planning phase: Planner creates the diagnostic plan
        if last_speaker == self.agents["planner"] and self.state.current_phase == "planning":
            self.state.current_phase = "diagnosing"
            self.state.diagnostic_round = 1
            return self.agents["k8s_diagnostician"]

        # Diagnosing phase: Cycle through diagnostic agents
        if self.state.current_phase == "diagnosing":
            if last_speaker == self.agents["k8s_diagnostician"]:
                return self.agents["sls_diagnostician"]
            elif last_speaker == self.agents["sls_diagnostician"]:
                return self.agents["arms_diagnostician"]
            elif last_speaker == self.agents["arms_diagnostician"]:
                # Check if we have enough diagnostic data
                if self.state.diagnostic_round >= 1:
                    self.state.current_phase = "executing"
                    return self.agents["executor"]
                else:
                    # Continue with another round of diagnostics
                    self.state.diagnostic_round += 1
                    return self.agents["k8s_diagnostician"]

        # Executing phase: Executor proposes and implements solutions
        if last_speaker == self.agents["executor"] and self.state.current_phase == "executing":
            self.state.current_phase = "validating"
            return self.agents["validator"]

        # Validating phase: Validator checks results and decides next steps
        if last_speaker == self.agents["validator"] and self.state.current_phase == "validating":
            # Check if validation indicates we need more diagnostics
            last_message = messages[-1]["content"] if messages else ""
            if "need more data" in last_message.lower() or "insufficient" in last_message.lower():
                self.state.current_phase = "diagnosing"
                self.state.diagnostic_round += 1
                return self.agents["k8s_diagnostician"]
            else:
                self.state.current_phase = "completed"
                return None  # End conversation

        # Default fallback
        return self.agents["planner"]

    def create_group_chat(self, max_rounds: int = 25) -> autogen.GroupChat:
        """Create and configure the group chat for the workflow."""
        if not self._initialized:
            raise RuntimeError(
                "Workflow must be initialized before creating group chat")

        all_agents = list(self.agents.values())

        return autogen.GroupChat(
            agents=all_agents,
            messages=[],
            max_round=max_rounds,
            speaker_selection_method='auto',  # Use auto for now to avoid validation issues
            allow_repeat_speaker=False
        )

    def create_manager(self, groupchat: autogen.GroupChat) -> autogen.GroupChatManager:
        """Create the group chat manager."""
        return autogen.GroupChatManager(
            groupchat=groupchat,
            llm_config=get_llm_config()
        )

    async def cleanup(self):
        """Cleanup MCP workbenches."""
        for workbench in self.workbenches.values():
            if hasattr(workbench, 'close'):
                await workbench.close()


class OOMDiagnosticWorkflow:
    """
    Manages the OOM diagnostic workflow using state-driven group chat (legacy version).
    Implements the "Diagnose → Propose → Execute → Verify" loop.
    """

    def __init__(self):
        self.state = AgentState()
        self.agents = self._initialize_agents()
        self.diagnostic_agents = [
            self.agents["k8s_diagnostician"],
            self.agents["sls_diagnostician"],
            self.agents["arms_diagnostician"]
        ]
        self.diagnostic_reports = {}

    def _initialize_agents(self) -> Dict[str, autogen.Agent]:
        """Initialize all agents for the workflow."""
        return {
            "planner": get_planner_agent(),
            "k8s_diagnostician": get_k8s_diagnostician(),
            "sls_diagnostician": get_sls_diagnostician(),
            "arms_diagnostician": get_arms_diagnostician(),
            "executor": get_executor_agent(),
            "validator": get_validator_agent()
        }

    def custom_speaker_selection(
        self,
        last_speaker: autogen.Agent,
        groupchat: autogen.GroupChat
    ) -> autogen.Agent:
        """
        Custom speaker selection function implementing state-driven workflow.

        Args:
            last_speaker: The agent who spoke last
            groupchat: The group chat instance

        Returns:
            Next agent to speak
        """
        messages = groupchat.messages

        # Initial state: Start with planner
        if not messages or last_speaker is None:
            self.state.current_phase = "planning"
            return self.agents["planner"]

        # Planning phase: Planner creates the diagnostic plan
        if last_speaker == self.agents["planner"] and self.state.current_phase == "planning":
            self.state.current_phase = "diagnosing"
            # Start with K8s diagnostician
            return self.agents["k8s_diagnostician"]

        # Diagnosing phase: Rotate through diagnostic agents
        if last_speaker in self.diagnostic_agents and self.state.current_phase == "diagnosing":
            # Record the diagnostic report
            domain = last_speaker.name.replace("_Diagnostician", "").lower()
            if domain not in self.state.completed_diagnostics:
                self.state.completed_diagnostics.append(domain)
                self.diagnostic_reports[last_speaker.name] = messages[-1]['content']

            # Check if all diagnostics are complete
            if len(self.state.completed_diagnostics) >= 3:
                self.state.current_phase = "executing"
                return self.agents["executor"]
            else:
                # Move to next diagnostic agent
                current_index = self.diagnostic_agents.index(last_speaker)
                next_index = (current_index + 1) % len(self.diagnostic_agents)
                next_agent = self.diagnostic_agents[next_index]

                # Skip if this agent has already completed
                domain = next_agent.name.replace("_Diagnostician", "").lower()
                if domain in self.state.completed_diagnostics:
                    # Find the next uncompleted agent
                    for agent in self.diagnostic_agents:
                        agent_domain = agent.name.replace(
                            "_Diagnostician", "").lower()
                        if agent_domain not in self.state.completed_diagnostics:
                            return agent
                    # All done, move to execution
                    self.state.current_phase = "executing"
                    return self.agents["executor"]

                return next_agent

        # Executing phase: Executor performs remediation
        if last_speaker == self.agents["executor"] and self.state.current_phase == "executing":
            self.state.current_phase = "validating"
            return self.agents["validator"]

        # Validating phase: Validator generates final report
        if last_speaker == self.agents["validator"] and self.state.current_phase == "validating":
            self.state.current_phase = "completed"
            # Workflow complete - return None to end
            return None

        # Default fallback
        return self.agents["planner"]

    def create_group_chat(self, max_rounds: int = 20) -> autogen.GroupChat:
        """
        Create and configure the group chat for the workflow.

        Args:
            max_rounds: Maximum number of conversation rounds

        Returns:
            Configured GroupChat instance
        """
        all_agents = list(self.agents.values())

        groupchat = autogen.GroupChat(
            agents=all_agents,
            messages=[],
            max_round=max_rounds,
            speaker_selection_method=self.custom_speaker_selection,
            allow_repeat_speaker=False
        )

        return groupchat

    def create_manager(self, groupchat: autogen.GroupChat) -> autogen.GroupChatManager:
        """
        Create the group chat manager.

        Args:
            groupchat: The group chat to manage

        Returns:
            Configured GroupChatManager instance
        """
        return autogen.GroupChatManager(
            groupchat=groupchat,
            llm_config=get_llm_config()
        )

    def is_termination_message(self, message: Dict[str, Any]) -> bool:
        """
        Check if a message indicates workflow termination.

        Args:
            message: Message to check

        Returns:
            True if workflow should terminate
        """
        content = message.get("content", "").upper()

        # Check for explicit termination signals
        termination_signals = [
            "FINAL REPORT",
            "WORKFLOW COMPLETE",
            "TERMINATE",
            "RESOLUTION COMPLETE"
        ]

        for signal in termination_signals:
            if signal in content:
                return True

        # Check if we're in completed state
        if self.state.current_phase == "completed":
            return True

        return False

    def get_workflow_status(self) -> Dict[str, Any]:
        """
        Get current workflow status.

        Returns:
            Dictionary with current workflow state
        """
        return {
            "current_phase": self.state.current_phase,
            "completed_diagnostics": self.state.completed_diagnostics,
            "total_diagnostics": len(self.diagnostic_agents),
            "diagnostic_completion": len(self.state.completed_diagnostics) / len(self.diagnostic_agents),
            "agents_initialized": len(self.agents),
            "workflow_complete": self.state.current_phase == "completed"
        }

    def reset_workflow(self):
        """Reset the workflow state for a new diagnostic session."""
        self.state = AgentState()
        self.diagnostic_reports = {}
