"""
Configuration module for AutoGen Diagnostic System.
Loads configuration from environment variables and .env file.
"""

import os
from dotenv import load_dotenv
from typing import Dict, Any
from autogen_ext.tools.mcp import StreamableHttpServerParams

# Load .env file from project root
load_dotenv()

# Qwen LLM Configuration
QWEN_API_KEY = os.getenv("QWEN_API_KEY", "EMPTY")
QWEN_BASE_URL = os.getenv("QWEN_BASE_URL")
QWEN_MODEL_NAME = os.getenv("QWEN_MODEL_NAME", "qwen-72b-chat")

# MCP Server Configuration
MCP_SERVER_URL = os.getenv("MCP_SERVER_URL", "http://localhost:8080/mcp")
MCP_SERVER_HEADERS = {
    "Content-Type": "application/json",
    "Authorization": os.getenv("MCP_SERVER_AUTH", "Bearer your-token-here")
}
MCP_TIMEOUT = float(os.getenv("MCP_TIMEOUT", "30.0"))
MCP_SSE_READ_TIMEOUT = float(os.getenv("MCP_SSE_READ_TIMEOUT", "300.0"))

# Legacy Tool Server URLs (for backward compatibility)
K8S_TOOL_SERVER_URL = os.getenv(
    "K8S_TOOL_SERVER_URL", "http://localhost:8081/diagnose")
SLS_TOOL_SERVER_URL = os.getenv(
    "SLS_TOOL_SERVER_URL", "http://localhost:8082/diagnose")
ARMS_TOOL_SERVER_URL = os.getenv(
    "ARMS_TOOL_SERVER_URL", "http://localhost:8083/diagnose")

# Default Configuration
DEFAULT_NAMESPACE = os.getenv("DEFAULT_NAMESPACE", "default")
DEFAULT_REGION_ID = os.getenv("DEFAULT_REGION_ID", "cn-hangzhou")
CLUSTER_ID = os.getenv("CLUSTER_ID", "")
NODE_POOL_ID = os.getenv("NODE_POOL_ID", "")

# Logging Configuration
LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
LOG_FILE = os.getenv("LOG_FILE", "logs/autogen_diagnostic.log")

# Validate required configuration
if not QWEN_BASE_URL:
    raise ValueError("QWEN_BASE_URL must be set in the .env file.")

# AutoGen LLM Configuration
llm_config: Dict[str, Any] = {
    "config_list": [
        {
            "model": QWEN_MODEL_NAME,
            "api_key": QWEN_API_KEY,
            "base_url": QWEN_BASE_URL,
        }
    ],
    "temperature": 0.1,  # Low temperature for deterministic output
    "timeout": 120,      # API request timeout in seconds
}

# Enhanced LLM config for validator with structured output
validator_llm_config: Dict[str, Any] = llm_config.copy()


def get_llm_config() -> Dict[str, Any]:
    """Get the standard LLM configuration."""
    return llm_config.copy()


def get_validator_llm_config() -> Dict[str, Any]:
    """Get the validator LLM configuration with structured output support."""
    return validator_llm_config.copy()


def get_tool_server_urls() -> Dict[str, str]:
    """Get all tool server URLs."""
    return {
        "k8s": K8S_TOOL_SERVER_URL,
        "sls": SLS_TOOL_SERVER_URL,
        "arms": ARMS_TOOL_SERVER_URL,
    }


def get_cluster_config() -> Dict[str, str]:
    """Get cluster configuration for node pool operations."""
    return {
        "cluster_id": CLUSTER_ID,
        "node_pool_id": NODE_POOL_ID,
        "region_id": DEFAULT_REGION_ID,
    }


def get_mcp_server_params() -> StreamableHttpServerParams:
    """Get MCP server parameters for connecting to diagnostic tools."""
    return StreamableHttpServerParams(
        url=MCP_SERVER_URL,
        headers=MCP_SERVER_HEADERS,
        timeout=MCP_TIMEOUT,
        sse_read_timeout=MCP_SSE_READ_TIMEOUT,
        terminate_on_close=True
    )
