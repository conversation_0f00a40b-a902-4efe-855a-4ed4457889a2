"""
Pydantic models for structured data exchange between agents.
"""

from pydantic import BaseModel, Field
from typing import List, Dict, Any, Literal, Optional
from datetime import datetime


class DiagnosticReport(BaseModel):
    """Structured diagnostic report from domain experts."""
    domain: str = Field(...,
                        description="The domain of the diagnosis (e.g., 'k8s', 'sls', 'arms').")
    findings: List[str] = Field(...,
                                description="A list of key findings from the diagnosis.")
    raw_data: Dict[str, Any] = Field(
        ..., description="The raw data returned by the diagnostic tool.")
    timestamp: datetime = Field(
        default_factory=datetime.now, description="When the diagnosis was performed.")
    status: Literal["success", "error", "partial"] = Field(
        ..., description="Status of the diagnostic operation.")


class ExecutionPlan(BaseModel):
    """Structured execution plan for OOM problem resolution."""
    command_type: Literal["kubectl", "nodepool_scale", "combined"] = Field(
        ..., description="The type of operation to execute.")
    operations: List[Dict[str, Any]
                     ] = Field(..., description="List of operations to be executed.")
    justification: str = Field(
        ..., description="Why this plan is expected to solve the OOM problem.")
    estimated_impact: str = Field(...,
                                  description="Expected impact and timeline for the fix.")
    rollback_plan: Optional[str] = Field(
        None, description="How to rollback if the plan fails.")


class OOMDiagnosticInput(BaseModel):
    """Input parameters for OOM diagnostic process."""
    namespace: str = Field(
        default="default", description="Kubernetes namespace")
    sls_project: str = Field(..., description="SLS log project name")
    sls_logStore: str = Field(..., description="SLS log store name")
    arms_project: str = Field(..., description="ARMS/CMS Project name")
    arms_metricStore: str = Field(...,
                                  description="Prometheus metric store name")
    regionId: str = Field(...,
                          description="Cloud region ID (e.g., 'cn-hangzhou')")
    clusterID: str = Field(..., description="Cluster ID for node pool scaling")
    nodePoolID: str = Field(...,
                            description="Node pool ID for scaling operations")
    problem_description: Optional[str] = Field(
        None, description="Additional problem description")


class FinalReport(BaseModel):
    """Final structured report for OOM problem resolution."""
    is_resolved: bool = Field(...,
                              description="Was the OOM problem successfully resolved?")
    confidence_score: float = Field(..., ge=0.0, le=1.0,
                                    description="Confidence in the resolution, from 0.0 to 1.0.")
    summary: str = Field(
        ..., description="Concise summary of the problem, actions taken, and outcome.")
    root_cause: str = Field(...,
                            description="Identified root cause of the OOM issue.")
    operations_performed: List[str] = Field(
        ..., description="List of all operations that were executed.")
    current_status: str = Field(
        ..., description="Current health status of pods, nodes, and services.")
    long_term_recommendations: List[str] = Field(
        ..., description="Recommendations to prevent future OOM issues.")
    reasoning: str = Field(...,
                           description="Detailed reasoning for the resolution conclusion.")


class AgentState(BaseModel):
    """State tracking for the diagnostic workflow."""
    current_phase: Literal["planning", "diagnosing", "executing",
                           "validating", "completed"] = Field(default="planning")
    completed_diagnostics: List[str] = Field(
        default_factory=list, description="List of completed diagnostic domains")
    diagnostic_reports: Dict[str, DiagnosticReport] = Field(
        default_factory=dict)
    execution_plan: Optional[ExecutionPlan] = Field(None)
    diagnostic_round: int = Field(
        default=0, description="Current round of diagnostic analysis")
    execution_results: List[Dict[str, Any]] = Field(default_factory=list)
    final_report: Optional[FinalReport] = Field(None)
    error_messages: List[str] = Field(default_factory=list)


class ToolCallResult(BaseModel):
    """Result from external tool calls."""
    tool_name: str = Field(..., description="Name of the tool that was called")
    success: bool = Field(...,
                          description="Whether the tool call was successful")
    result: Dict[str, Any] = Field(...,
                                   description="Raw result data from the tool")
    error_message: Optional[str] = Field(
        None, description="Error message if the call failed")
    execution_time: float = Field(
        ..., description="Time taken to execute the tool call in seconds")
