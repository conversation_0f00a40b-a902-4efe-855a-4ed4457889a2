#!/usr/bin/env python3
"""
Demo runner for the AutoGen OOM Diagnostic System.
Starts mock tool servers and runs the diagnostic workflow.
"""

import asyncio
import time
import sys
import signal
import threading
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.tools.mock_servers import start_mock_server_background
from main import OOMDiagnosticSystem, setup_logging

def signal_handler(signum, frame):
    """Handle interrupt signals gracefully."""
    print("\nShutting down demo...")
    sys.exit(0)

async def run_demo():
    """Run the complete demo workflow."""
    print("="*80)
    print("AutoGen OOM Diagnostic System - Demo")
    print("="*80)
    
    # Setup logging
    setup_logging()
    
    # Start mock tool server in background
    print("Starting mock diagnostic tool server on port 8080...")
    server_thread = start_mock_server_background(8080)
    
    # Wait for server to start
    print("Waiting for mock server to start...")
    time.sleep(2)
    
    # Test problem scenarios
    test_scenarios = [
        {
            "name": "Production OOM Crisis",
            "description": """
            The 'checkout-service' deployment in the 'production' namespace is experiencing frequent OOMKilled events.
            Pods are restarting every few minutes and the service is becoming unavailable.
            Memory usage appears to be hitting the 2Gi limit consistently.
            Customer complaints are increasing due to checkout failures.
            """
        },
        {
            "name": "Development Memory Leak",
            "description": """
            Memory usage in the 'user-service' pods in 'development' namespace is gradually increasing.
            No immediate OOM events but memory consumption grows by 100MB every hour.
            Suspected memory leak in the application code.
            """
        }
    ]
    
    # Create diagnostic system
    system = OOMDiagnosticSystem()
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n{'='*60}")
        print(f"SCENARIO {i}: {scenario['name']}")
        print(f"{'='*60}")
        print(f"Problem: {scenario['description'].strip()}")
        print("\nStarting diagnostic workflow...")
        
        try:
            # Run diagnostic
            result = await system.run_diagnostic(scenario['description'])
            
            if result["success"]:
                print(f"\n✅ Diagnostic workflow completed successfully!")
                print(f"📊 Workflow Status: {result['workflow_status']}")
                
                # Show summary
                workflow_status = result['workflow_status']
                print(f"\n📈 Summary:")
                print(f"   - Current Phase: {workflow_status['current_phase']}")
                print(f"   - Diagnostics Completed: {workflow_status['completed_diagnostics']}")
                print(f"   - Completion Rate: {workflow_status['diagnostic_completion']:.1%}")
                print(f"   - Workflow Complete: {workflow_status['workflow_complete']}")
                
            else:
                print(f"\n❌ Diagnostic workflow failed: {result['error']}")
                
        except Exception as e:
            print(f"\n💥 Unexpected error: {str(e)}")
        
        # Wait between scenarios
        if i < len(test_scenarios):
            print(f"\nWaiting 3 seconds before next scenario...")
            await asyncio.sleep(3)
    
    print(f"\n{'='*80}")
    print("Demo completed! Mock server is still running on port 8080")
    print("You can test the diagnostic endpoints manually:")
    print("  curl -X POST http://localhost:8080/k8s/diagnose \\")
    print("       -H 'Content-Type: application/json' \\")
    print("       -d '{\"problem\": \"OOM issues\", \"namespace\": \"default\", \"domain\": \"k8s\"}'")
    print(f"{'='*80}")

def main():
    """Main function to run the demo."""
    # Setup signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # Run the demo
        asyncio.run(run_demo())
        
        # Keep the program running to maintain mock server
        print("\nPress Ctrl+C to exit...")
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\nDemo interrupted by user.")
    except Exception as e:
        print(f"Demo failed: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
