# Qwen LLM Configuration
QWEN_API_KEY=your_qwen_api_key_here
QWEN_BASE_URL=http://localhost:8000/v1
QWEN_MODEL_NAME=qwen-72b-chat

# MCP Server Configuration (Streamable HTTP)
MCP_SERVER_URL=http://localhost:8080/mcp
MCP_SERVER_AUTH=Bearer your-mcp-token-here
MCP_TIMEOUT=30.0
MCP_SSE_READ_TIMEOUT=300.0

# Legacy Tool Server URLs (for backward compatibility)
K8S_TOOL_SERVER_URL=http://localhost:8080/k8s/diagnose
SLS_TOOL_SERVER_URL=http://localhost:8080/sls/diagnose
ARMS_TOOL_SERVER_URL=http://localhost:8080/arms/diagnose

# Alternative: Use separate servers for production
# K8S_TOOL_SERVER_URL=http://localhost:8081/diagnose
# SLS_TOOL_SERVER_URL=http://localhost:8082/diagnose
# ARMS_TOOL_SERVER_URL=http://localhost:8083/diagnose

# Default K8s Configuration
DEFAULT_NAMESPACE=default
DEFAULT_REGION_ID=cn-hangzhou

# Cluster Configuration (for node pool scaling)
CLUSTER_ID=your_cluster_id_here
NODE_POOL_ID=your_node_pool_id_here

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/autogen_diagnostic.log
