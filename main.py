"""
Main entry point for the AutoGen OOM Diagnostic System.
Implements a Multi-Agent k8s OOM problem closed-loop troubleshooting system.
"""

import autogen
from src.core.models import OOMDiagnosticInput
from src.core.config import LOG_LEVEL, LOG_FILE
from src.core.workflow import OOMDiagnosticWorkflow, OOMDiagnosticWorkflowMCP
import asyncio
import logging
import json
import sys
from pathlib import Path
from typing import Dict, Any, Optional

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))


def setup_logging():
    """Configure logging for the application."""
    # Create logs directory if it doesn't exist
    log_path = Path(LOG_FILE)
    log_path.parent.mkdir(exist_ok=True)

    # Configure logging
    logging.basicConfig(
        level=getattr(logging, LOG_LEVEL),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(LOG_FILE),
            logging.StreamHandler(sys.stdout)
        ]
    )

    # Configure AutoGen logging
    try:
        trace_logger = logging.getLogger(autogen.agentchat.TRACE_LOGGER_NAME)
        trace_logger.setLevel(logging.INFO)
        trace_logger.addHandler(logging.StreamHandler())
    except AttributeError:
        # Fallback for newer AutoGen versions
        trace_logger = logging.getLogger("autogen.trace")
        trace_logger.setLevel(logging.INFO)
        trace_logger.addHandler(logging.StreamHandler())


class OOMDiagnosticSystemMCP:
    """Main system class for OOM diagnostic operations using MCP."""

    def __init__(self):
        self.workflow = OOMDiagnosticWorkflowMCP()
        self.logger = logging.getLogger(__name__)

    def parse_problem_input(self, problem_description: str) -> OOMDiagnosticInput:
        """
        Parse user input to extract diagnostic parameters.
        In a real implementation, this would use NLP or structured input.

        Args:
            problem_description: User's problem description

        Returns:
            Structured diagnostic input
        """
        # For demo purposes, use default values
        # In production, this would parse the description or prompt for missing values
        return OOMDiagnosticInput(
            namespace="default",
            sls_project="demo-sls-project",
            sls_logStore="demo-logstore",
            arms_project="demo-arms-project",
            arms_metricStore="demo-metricstore",
            regionId="cn-hangzhou",
            clusterID="demo-cluster-id",
            nodePoolID="demo-nodepool-id",
            problem_description=problem_description
        )

    async def run_diagnostic(self, problem_description: str) -> Dict[str, Any]:
        """
        Run the complete OOM diagnostic workflow using MCP.

        Args:
            problem_description: Description of the OOM problem

        Returns:
            Dictionary containing the diagnostic results
        """
        try:
            self.logger.info(
                f"Starting OOM diagnostic for: {problem_description}")

            # Initialize the workflow
            await self.workflow.initialize()

            # Parse input
            diagnostic_input = self.parse_problem_input(problem_description)

            # Create group chat and manager
            groupchat = self.workflow.create_group_chat(max_rounds=25)
            manager = self.workflow.create_manager(groupchat)

            # Start the diagnostic conversation
            initial_message = f"""
            OOM Problem Analysis Request:

            Problem Description: {problem_description}

            Diagnostic Parameters:
            - Namespace: {diagnostic_input.namespace}
            - SLS Project: {diagnostic_input.sls_project}
            - ARMS Project: {diagnostic_input.arms_project}
            - Cluster ID: {diagnostic_input.clusterID}
            - Region: {diagnostic_input.regionId}

            Please begin the diagnostic analysis following the standard workflow:
            1. Planning phase - Create diagnostic strategy
            2. Diagnosis phase - Gather data from K8s, SLS, and ARMS
            3. Execution phase - Propose and implement solutions
            4. Validation phase - Verify results and provide final report
            """

            # Run the conversation
            result = await manager.a_initiate_chat(
                self.workflow.agents["planner"],
                message=initial_message,
                max_turns=25
            )

            # Extract final state
            final_state = {
                "status": "completed",
                "phase": self.workflow.state.current_phase,
                "diagnostic_reports": {k: v.model_dump() for k, v in self.workflow.state.diagnostic_reports.items()},
                "execution_plan": self.workflow.state.execution_plan.model_dump() if self.workflow.state.execution_plan else None,
                "final_report": self.workflow.state.final_report.model_dump() if self.workflow.state.final_report else None,
                "conversation_history": [msg for msg in groupchat.messages],
                "error_messages": self.workflow.state.error_messages
            }

            self.logger.info("OOM diagnostic completed successfully")
            return final_state

        except Exception as e:
            self.logger.error(f"Error during diagnostic: {str(e)}")
            return {
                "status": "error",
                "error": str(e),
                "phase": self.workflow.state.current_phase if hasattr(self, 'workflow') else "unknown"
            }
        finally:
            # Cleanup MCP workbenches
            if hasattr(self, 'workflow'):
                await self.workflow.cleanup()


class OOMDiagnosticSystem:
    """Main system class for OOM diagnostic operations (legacy version)."""

    def __init__(self):
        self.workflow = OOMDiagnosticWorkflow()
        self.logger = logging.getLogger(__name__)

    def parse_problem_input(self, problem_description: str) -> OOMDiagnosticInput:
        """
        Parse user input to extract diagnostic parameters.
        In a real implementation, this would use NLP or structured input.

        Args:
            problem_description: User's problem description

        Returns:
            Structured diagnostic input
        """
        # For demo purposes, use default values
        # In production, this would parse the description or prompt for missing values
        return OOMDiagnosticInput(
            namespace="default",
            sls_project="demo-sls-project",
            sls_logStore="demo-logstore",
            arms_project="demo-arms-project",
            arms_metricStore="demo-metricstore",
            regionId="cn-hangzhou",
            clusterID="demo-cluster-id",
            nodePoolID="demo-nodepool-id",
            problem_description=problem_description
        )

    async def run_diagnostic(self, problem_description: str) -> Dict[str, Any]:
        """
        Run the complete OOM diagnostic workflow.

        Args:
            problem_description: Description of the OOM problem

        Returns:
            Dictionary with workflow results
        """
        self.logger.info(f"Starting OOM diagnostic for: {problem_description}")

        try:
            # Parse input parameters
            diagnostic_input = self.parse_problem_input(problem_description)
            self.logger.info(
                f"Parsed diagnostic input: {diagnostic_input.model_dump()}")

            # Reset workflow state
            self.workflow.reset_workflow()

            # Create group chat and manager
            groupchat = self.workflow.create_group_chat(max_rounds=25)
            manager = self.workflow.create_manager(groupchat)

            # Create user proxy to initiate the conversation
            user_proxy = autogen.UserProxyAgent(
                name="User_Proxy",
                human_input_mode="NEVER",
                code_execution_config=False,
                is_termination_msg=self.workflow.is_termination_message,
                description="User proxy to initiate the OOM diagnostic workflow."
            )

            # Prepare the initial message with problem details
            initial_message = f"""
OOM Problem Report:

Problem Description: {problem_description}

Diagnostic Parameters:
- Namespace: {diagnostic_input.namespace}
- SLS Project: {diagnostic_input.sls_project}
- SLS LogStore: {diagnostic_input.sls_logStore}
- ARMS Project: {diagnostic_input.arms_project}
- ARMS MetricStore: {diagnostic_input.arms_metricStore}
- Region ID: {diagnostic_input.regionId}
- Cluster ID: {diagnostic_input.clusterID}
- Node Pool ID: {diagnostic_input.nodePoolID}

Please begin the OOM diagnostic workflow following the "Diagnose → Propose → Execute → Verify" process.
            """

            self.logger.info("Initiating group chat workflow...")

            # Start the conversation
            result = await user_proxy.a_initiate_chat(
                manager,
                message=initial_message.strip()
            )

            # Get final workflow status
            workflow_status = self.workflow.get_workflow_status()

            self.logger.info(
                f"Workflow completed with status: {workflow_status}")

            return {
                "success": True,
                "workflow_status": workflow_status,
                "conversation_result": result,
                "diagnostic_input": diagnostic_input.model_dump()
            }

        except Exception as e:
            self.logger.error(
                f"Error during diagnostic workflow: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e),
                "workflow_status": self.workflow.get_workflow_status()
            }


async def main():
    """Main function to run the OOM diagnostic system."""
    setup_logging()
    logger = logging.getLogger(__name__)

    logger.info("Starting AutoGen OOM Diagnostic System")

    # Example problem description
    problem_description = """
    The 'checkout-service' deployment in the 'production' namespace is experiencing frequent OOMKilled events.
    Pods are restarting every few minutes and the service is becoming unavailable.
    Memory usage appears to be hitting the 2Gi limit consistently.
    """

    # Create and run the diagnostic system (using MCP version)
    system = OOMDiagnosticSystemMCP()

    try:
        # Run the diagnostic workflow
        result = await system.run_diagnostic(problem_description)

        if result["status"] == "completed":
            logger.info("OOM diagnostic workflow completed successfully")
            print("\n" + "="*80)
            print("OOM DIAGNOSTIC WORKFLOW COMPLETED")
            print("="*80)
            print(f"Workflow Status: {result['status']}")
            print(f"Final Phase: {result['phase']}")
            if result.get('final_report'):
                print(f"Final Report: {result['final_report']}")
        else:
            logger.error(
                f"OOM diagnostic workflow failed: {result.get('error', 'Unknown error')}")
            print(f"\nWorkflow failed: {result.get('error', 'Unknown error')}")

    except KeyboardInterrupt:
        logger.info("Process interrupted by user")
        print("\nProcess interrupted.")
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}", exc_info=True)
        print(f"\nUnexpected error: {str(e)}")


if __name__ == "__main__":
    asyncio.run(main())
